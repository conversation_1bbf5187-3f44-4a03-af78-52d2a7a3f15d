const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

/**
 * Aggressive Marine Traffic Scraper
 * Mencoba berbagai metode untuk mendapatkan SEMUA data yang tersedia
 */
class AggressiveMarineTrafficScraper {
    constructor(options = {}) {
        this.options = {
            headless: true,
            sessionPath: path.join(__dirname, 'sessions', 'session.json'),
            outputPath: path.join(__dirname, `marine-traffic-aggressive-${Date.now()}.json`),
            timeout: 30000,
            maxRetries: 3,
            delay: 1000,
            ...options
        };
        
        this.browser = null;
        this.context = null;
        this.page = null;
        this.allVessels = [];
    }

    async init() {
        try {
            console.log('🚀 Initializing Aggressive Marine Traffic Scraper...');
            
            this.browser = await chromium.launch({
                headless: this.options.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.context = await this.browser.newContext({
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
                viewport: { width: 1920, height: 1080 },
                locale: 'en-US',
                timezoneId: 'Asia/Jakarta'
            });

            await this.loadSession();
            this.page = await this.context.newPage();

            await this.page.setExtraHTTPHeaders({
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest'
            });

            console.log('✅ Browser initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing browser:', error.message);
            throw error;
        }
    }

    async loadSession() {
        try {
            const sessionData = await fs.readFile(this.options.sessionPath, 'utf8');
            const cookies = JSON.parse(sessionData);
            await this.context.addCookies(cookies);
            console.log(`🍪 Loaded ${cookies.length} cookies from session`);
        } catch (error) {
            console.error('❌ Error loading session:', error.message);
            throw error;
        }
    }

    /**
     * Try different API endpoints and parameters
     */
    async tryDifferentEndpoints() {
        const endpoints = [
            // Original endpoint with different parameters
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in',
            
            // Try without filters
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID',
            
            // Try with different page sizes
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&per_page=1000',
            
            // Try with different limits
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&limit=10000',
            
            // Try data endpoint instead of reports
            'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID',
        ];

        let bestResult = null;
        let maxVessels = 0;

        for (let i = 0; i < endpoints.length; i++) {
            const endpoint = endpoints[i];
            console.log(`\n🔍 Trying endpoint ${i + 1}/${endpoints.length}:`);
            console.log(`   ${endpoint.substring(0, 100)}...`);
            
            try {
                const result = await this.fetchFromEndpoint(endpoint);
                if (result && result.data && result.data.length > maxVessels) {
                    maxVessels = result.data.length;
                    bestResult = result;
                    console.log(`✅ New best result: ${result.data.length} vessels`);
                } else {
                    console.log(`📊 Result: ${result?.data?.length || 0} vessels`);
                }
                
                // Delay between requests
                await new Promise(resolve => setTimeout(resolve, this.options.delay));
                
            } catch (error) {
                console.log(`❌ Failed: ${error.message}`);
            }
        }

        return bestResult;
    }

    /**
     * Fetch data from specific endpoint
     */
    async fetchFromEndpoint(url) {
        try {
            const response = await this.page.goto(url, {
                waitUntil: 'networkidle',
                timeout: this.options.timeout
            });

            if (!response.ok()) {
                throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
            }

            const responseText = await this.page.content();
            
            let data;
            if (responseText.includes('<pre>') && responseText.includes('</pre>')) {
                const jsonMatch = responseText.match(/<pre>(.*?)<\/pre>/s);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[1]);
                } else {
                    throw new Error('Could not extract JSON from HTML');
                }
            } else {
                data = JSON.parse(responseText);
            }

            return data;

        } catch (error) {
            throw new Error(`Endpoint failed: ${error.message}`);
        }
    }

    /**
     * Try pagination with different strategies
     */
    async tryPaginationStrategies() {
        console.log('\n🔄 Trying pagination strategies...');
        
        const strategies = [
            // Strategy 1: offset-based pagination
            {
                name: 'Offset-based',
                getUrl: (page, size) => `https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&offset=${(page - 1) * size}&limit=${size}`
            },
            // Strategy 2: page-based pagination
            {
                name: 'Page-based',
                getUrl: (page, size) => `https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&page=${page}&per_page=${size}`
            },
            // Strategy 3: start parameter
            {
                name: 'Start-based',
                getUrl: (page, size) => `https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&start=${(page - 1) * size}&length=${size}`
            }
        ];

        let bestResult = { data: [] };
        
        for (const strategy of strategies) {
            console.log(`\n📄 Trying ${strategy.name} pagination...`);
            
            try {
                const result = await this.paginateWithStrategy(strategy);
                if (result.data.length > bestResult.data.length) {
                    bestResult = result;
                    console.log(`✅ ${strategy.name} is new best: ${result.data.length} vessels`);
                }
            } catch (error) {
                console.log(`❌ ${strategy.name} failed: ${error.message}`);
            }
        }

        return bestResult;
    }

    /**
     * Paginate with specific strategy
     */
    async paginateWithStrategy(strategy) {
        const allVessels = [];
        const pageSize = 500;
        const maxPages = 50;
        let page = 1;
        let hasMoreData = true;

        while (hasMoreData && page <= maxPages) {
            const url = strategy.getUrl(page, pageSize);
            
            try {
                const data = await this.fetchFromEndpoint(url);
                
                if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
                    allVessels.push(...data.data);
                    console.log(`   Page ${page}: ${data.data.length} vessels (Total: ${allVessels.length})`);
                    
                    if (data.data.length < pageSize) {
                        hasMoreData = false;
                    }
                } else {
                    hasMoreData = false;
                }
                
                await new Promise(resolve => setTimeout(resolve, this.options.delay));
                page++;
                
            } catch (error) {
                console.log(`   Page ${page} failed: ${error.message}`);
                hasMoreData = false;
            }
        }

        // Remove duplicates
        const uniqueVessels = [];
        const seenIds = new Set();
        
        for (const vessel of allVessels) {
            if (vessel.SHIP_ID && !seenIds.has(vessel.SHIP_ID)) {
                seenIds.add(vessel.SHIP_ID);
                uniqueVessels.push(vessel);
            }
        }

        return {
            data: uniqueVessels,
            totalCollected: allVessels.length,
            uniqueCount: uniqueVessels.length
        };
    }

    async close() {
        try {
            if (this.page) await this.page.close();
            if (this.context) await this.context.close();
            if (this.browser) await this.browser.close();
            console.log('🧹 Browser closed successfully');
        } catch (error) {
            console.error('❌ Error closing browser:', error.message);
        }
    }

    async run() {
        try {
            await this.init();
            
            console.log('\n🎯 Starting aggressive data collection...\n');
            
            // Try different endpoints first
            console.log('=== PHASE 1: Testing Different Endpoints ===');
            const endpointResult = await this.tryDifferentEndpoints();
            
            // Try pagination strategies
            console.log('\n=== PHASE 2: Testing Pagination Strategies ===');
            const paginationResult = await this.tryPaginationStrategies();
            
            // Choose best result
            let finalResult;
            if (paginationResult.data.length > (endpointResult?.data?.length || 0)) {
                finalResult = paginationResult;
                console.log('\n🏆 Best result: Pagination strategy');
            } else {
                finalResult = endpointResult;
                console.log('\n🏆 Best result: Single endpoint');
            }
            
            // Save result
            await fs.writeFile(this.options.outputPath, JSON.stringify(finalResult, null, 2), 'utf8');
            
            console.log('\n📊 Final Results:');
            console.log(`   Total vessels collected: ${finalResult.data.length}`);
            console.log(`   Output file: ${this.options.outputPath}`);
            
            return finalResult;
            
        } catch (error) {
            console.error('❌ Aggressive scraper failed:', error.message);
            throw error;
        } finally {
            await this.close();
        }
    }
}

// Export and run
module.exports = { AggressiveMarineTrafficScraper };

if (require.main === module) {
    const scraper = new AggressiveMarineTrafficScraper({
        headless: true,
        delay: 2000
    });
    
    scraper.run()
        .then((result) => {
            console.log('\n🎉 Aggressive scraping completed!');
            console.log(`📊 Final count: ${result.data.length} vessels`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Aggressive scraping failed:', error.message);
            process.exit(1);
        });
}
