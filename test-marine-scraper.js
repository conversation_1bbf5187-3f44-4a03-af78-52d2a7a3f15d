const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

async function testMarineTrafficScraper() {
    let browser, context, page;

    try {
        console.log('🚀 Starting Marine Traffic Scraper Test...\n');

        // 1. Launch browser
        console.log('📱 Launching browser...');
        browser = await chromium.launch({
            headless: false, // Set to true for production
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        // 2. Create context with proper settings
        console.log('🔧 Creating browser context...');
        context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
            viewport: { width: 1920, height: 1080 },
            locale: 'en-US'
        });

        // 3. Load session cookies
        console.log('🍪 Loading session cookies...');
        const sessionPath = path.join(__dirname, 'sessions', 'session.json');
        const sessionData = await fs.readFile(sessionPath, 'utf8');
        const cookies = JSON.parse(sessionData);
        await context.addCookies(cookies);
        console.log(`✅ Loaded ${cookies.length} cookies`);

        // 4. Create page
        page = await context.newPage();

        // 5. Set additional headers
        console.log('📋 Setting request headers...');
        await page.setExtraHTTPHeaders({
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,id;q=0.8',
            'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-requested-with': 'XMLHttpRequest'
        });

        // 6. Make the API request
        console.log('🌐 Making request to Marine Traffic API...');
        const apiUrl = 'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in';

        // Method 1: Using page.goto
        const response = await page.goto(apiUrl, {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        console.log(`📊 Response status: ${response.status()}`);

        if (response.ok()) {
            // Get the response text first to check what we received
            const responseText = await page.content();
            console.log('📄 Response preview:', responseText.substring(0, 200) + '...');

            // Try to parse as JSON
            try {
                // Extract JSON from HTML response
                let jsonData;

                // Check if response is wrapped in HTML
                if (responseText.includes('<pre>') && responseText.includes('</pre>')) {
                    // Extract JSON from <pre> tags
                    const jsonMatch = responseText.match(/<pre>(.*?)<\/pre>/s);
                    if (jsonMatch) {
                        jsonData = JSON.parse(jsonMatch[1]);
                        console.log('✅ Successfully extracted JSON from HTML response!');
                    } else {
                        throw new Error('Could not extract JSON from HTML');
                    }
                } else {
                    // Try direct JSON parsing
                    jsonData = JSON.parse(responseText);
                }

                const data = jsonData;

                console.log('✅ Successfully fetched data!');
                console.log(`📈 Data structure:`, Object.keys(data));

                if (data.data && Array.isArray(data.data)) {
                    console.log(`🚢 Found ${data.data.length} vessels`);

                    // Show sample data
                    if (data.data.length > 0) {
                        console.log('\n📋 Sample vessel data:');
                        const sample = data.data[0];
                        console.log(JSON.stringify(sample, null, 2));
                    }
                }

                // Save data to file
                const outputPath = path.join(__dirname, 'marine-traffic-data.json');
                await fs.writeFile(outputPath, JSON.stringify(data, null, 2), 'utf8');
                console.log(`💾 Data saved to: ${outputPath}`);

                return data;

            } catch (jsonError) {
                console.error('❌ Error parsing JSON:', jsonError.message);

                // Save raw response for debugging
                const debugPath = path.join(__dirname, 'debug-response.html');
                await fs.writeFile(debugPath, responseText, 'utf8');
                console.log(`🐛 Raw response saved to: ${debugPath}`);
            }
        } else {
            console.error(`❌ HTTP Error: ${response.status()} - ${response.statusText()}`);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        console.log('\n🧹 Cleaning up...');
        if (page) await page.close();
        if (context) await context.close();
        if (browser) await browser.close();
        console.log('✅ Cleanup completed');
    }
}

// Alternative method using direct request
async function testDirectRequest() {
    let browser, context;

    try {
        console.log('\n🔄 Testing direct request method...\n');

        browser = await chromium.launch({ headless: true });
        context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36'
        });

        // Load cookies
        const sessionPath = path.join(__dirname, 'sessions', 'session.json');
        const sessionData = await fs.readFile(sessionPath, 'utf8');
        const cookies = JSON.parse(sessionData);
        await context.addCookies(cookies);

        const page = await context.newPage();

        // Make direct API request
        const response = await page.request.get(
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in',
            {
                headers: {
                    'accept': 'application/json, text/plain, */*',
                    'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                    'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'x-requested-with': 'XMLHttpRequest'
                }
            }
        );

        console.log(`📊 Direct request status: ${response.status()}`);

        if (response.ok()) {
            const data = await response.json();
            console.log('✅ Direct request successful!');

            if (data.data && Array.isArray(data.data)) {
                console.log(`🚢 Found ${data.data.length} vessels via direct request`);
            }

            // Save data
            const outputPath = path.join(__dirname, 'marine-traffic-data-direct.json');
            await fs.writeFile(outputPath, JSON.stringify(data, null, 2), 'utf8');
            console.log(`💾 Direct request data saved to: ${outputPath}`);

            return data;
        } else {
            const responseText = await response.text();
            console.error(`❌ Direct request failed: ${response.status()}`);
            console.error('Response:', responseText.substring(0, 500));
        }

    } catch (error) {
        console.error('❌ Direct request error:', error.message);
    } finally {
        if (context) await context.close();
        if (browser) await browser.close();
    }
}

// Main execution
async function main() {
    console.log('🌊 Marine Traffic Data Scraper Test Suite\n');
    console.log('==========================================\n');

    // Test both methods
    await testMarineTrafficScraper();
    await testDirectRequest();

    console.log('\n🎉 Test completed!');
}

// Run the test
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testMarineTrafficScraper, testDirectRequest };
