<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marine Traffic API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background: #28a745; color: white; }
        .post { background: #007bff; color: white; }
        .put { background: #ffc107; color: black; }
        .delete { background: #dc3545; color: white; }
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .example {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 Marine Traffic API</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="vessel-count">-</div>
                <div>Total Vessels</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="country-count">-</div>
                <div>Countries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="indonesian-count">-</div>
                <div>Indonesian Vessels</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="last-update">-</div>
                <div>Last Update</div>
            </div>
        </div>

        <h2>📖 API Endpoints</h2>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/vessels</code>
            <p>Mengambil semua data kapal</p>
            <button class="test-button" onclick="testAPI('/api/vessels')">Test</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/vessels/stats</code>
            <p>Mengambil statistik kapal</p>
            <button class="test-button" onclick="testAPI('/api/vessels/stats')">Test</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/vessels/search</code>
            <p>Mencari kapal berdasarkan kriteria</p>
            <div class="example">
                <strong>Query Parameters:</strong>
                <ul>
                    <li><code>name</code> - Nama kapal</li>
                    <li><code>country</code> - Negara</li>
                    <li><code>type</code> - Jenis kapal</li>
                    <li><code>port</code> - Pelabuhan</li>
                    <li><code>imo</code> - IMO number</li>
                    <li><code>mmsi</code> - MMSI number</li>
                </ul>
            </div>
            <button class="test-button" onclick="testAPI('/api/vessels/search?country=Indonesia')">Test (Indonesia)</button>
            <button class="test-button" onclick="testAPI('/api/vessels/search?type=Cargo')">Test (Cargo)</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/vessels/:id</code>
            <p>Mengambil data kapal berdasarkan SHIP_ID</p>
            <button class="test-button" onclick="testAPI('/api/vessels/1283618')">Test (ID: 1283618)</button>
        </div>

        <div class="endpoint">
            <span class="method post">POST</span>
            <code>/api/scrape</code>
            <p>Memulai proses scraping baru</p>
            <button class="test-button" onclick="startScraping()">Start Scraping</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/scrape/status</code>
            <p>Mengecek status scraping</p>
            <button class="test-button" onclick="testAPI('/api/scrape/status')">Check Status</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/files</code>
            <p>Melihat daftar file data yang tersedia</p>
            <button class="test-button" onclick="testAPI('/api/files')">Test</button>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/health</code>
            <p>Health check server</p>
            <button class="test-button" onclick="testAPI('/api/health')">Test</button>
        </div>

        <h2>📋 Response Example</h2>
        <div id="response" class="response">
            <p>Klik tombol "Test" pada endpoint di atas untuk melihat response</p>
        </div>

        <h2>🔍 Search Examples</h2>
        <div class="example">
            <strong>Mencari kapal berdasarkan nama:</strong><br>
            <code>GET /api/vessels/search?name=SAGA</code>
        </div>
        <div class="example">
            <strong>Mencari kapal Indonesia:</strong><br>
            <code>GET /api/vessels/search?country=Indonesia</code>
        </div>
        <div class="example">
            <strong>Mencari kapal cargo:</strong><br>
            <code>GET /api/vessels/search?type=Cargo</code>
        </div>
        <div class="example">
            <strong>Mencari kapal di Jakarta:</strong><br>
            <code>GET /api/vessels/search?port=JAKARTA</code>
        </div>

        <h2>📊 Data Format</h2>
        <p>Semua data menggunakan format yang sama dengan download.json dari MarineTraffic:</p>
        <div class="example">
            <pre><code>{
  "success": true,
  "total": 500,
  "data": [
    {
      "SHIP_ID": "1283618",
      "IMO": "7911545",
      "MMSI": "210121000",
      "CALLSIGN": "5BRC5",
      "SHIPNAME": "SAGA",
      "COUNTRY": "Cyprus",
      "TYPE_SUMMARY": "Passenger",
      "CURRENT_PORT": "BATU AMPAR",
      "LAT": "1.0983417",
      "LON": "103.89544",
      ...
    }
  ]
}</code></pre>
        </div>
    </div>

    <script>
        // Load initial stats
        loadStats();

        async function testAPI(endpoint) {
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = '<p>Loading...</p>';
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                responseDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Endpoint:</strong> ${endpoint}<br>
                    <strong>Response:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                responseDiv.innerHTML = `
                    <strong>Error:</strong> ${error.message}<br>
                    <strong>Endpoint:</strong> ${endpoint}
                `;
            }
        }

        async function startScraping() {
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = '<p>Starting scraping...</p>';
            
            try {
                const response = await fetch('/api/scrape', { method: 'POST' });
                const data = await response.json();
                
                responseDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Response:</strong><br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.success) {
                    // Check status periodically
                    checkScrapingStatus();
                }
            } catch (error) {
                responseDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function checkScrapingStatus() {
            try {
                const response = await fetch('/api/scrape/status');
                const data = await response.json();
                
                if (data.isInProgress) {
                    setTimeout(checkScrapingStatus, 5000); // Check again in 5 seconds
                } else {
                    loadStats(); // Reload stats when scraping is done
                }
            } catch (error) {
                console.error('Error checking scraping status:', error);
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/vessels/stats');
                if (response.ok) {
                    const data = await response.json();
                    const stats = data.statistics;
                    
                    document.getElementById('vessel-count').textContent = stats.total;
                    document.getElementById('country-count').textContent = Object.keys(stats.countries).length;
                    document.getElementById('indonesian-count').textContent = stats.indonesianVessels;
                    document.getElementById('last-update').textContent = new Date(data.timestamp).toLocaleDateString();
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Auto-refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>
