const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

class MarineTrafficScraper {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.sessionPath = path.join(__dirname, 'sessions', 'session.json');
    }

    async init() {
        try {
            // Launch browser
            this.browser = await chromium.launch({
                headless: true, // Set to false if you want to see the browser
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            // Create context
            this.context = await this.browser.newContext({
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                viewport: { width: 1920, height: 1080 },
                locale: 'en-US',
                timezoneId: 'Asia/Jakarta'
            });

            // Load and set cookies from session file
            await this.loadSession();

            // Create page
            this.page = await this.context.newPage();

            console.log('Browser initialized successfully');
        } catch (error) {
            console.error('Error initializing browser:', error);
            throw error;
        }
    }

    async loadSession() {
        try {
            const sessionData = await fs.readFile(this.sessionPath, 'utf8');
            const cookies = JSON.parse(sessionData);
            
            // Add cookies to context
            await this.context.addCookies(cookies);
            console.log(`Loaded ${cookies.length} cookies from session file`);
        } catch (error) {
            console.error('Error loading session:', error);
            throw error;
        }
    }

    async fetchMarineTrafficData() {
        try {
            const url = 'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in';
            
            // Set additional headers
            await this.page.setExtraHTTPHeaders({
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                'if-modified-since': 'Tue, 19 Aug 2025 05:12:07 GMT',
                'priority': 'u=1, i',
                'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest'
            });

            console.log('Making request to MarineTraffic API...');
            
            // Make the request
            const response = await this.page.goto(url, {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            if (!response.ok()) {
                throw new Error(`HTTP error! status: ${response.status()}`);
            }

            // Get response data
            const data = await response.json();
            console.log('Data fetched successfully');
            
            return data;

        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    }

    async saveDataToFile(data, filename = 'marine-traffic-data.json') {
        try {
            const outputPath = path.join(__dirname, filename);
            await fs.writeFile(outputPath, JSON.stringify(data, null, 2), 'utf8');
            console.log(`Data saved to: ${outputPath}`);
        } catch (error) {
            console.error('Error saving data:', error);
            throw error;
        }
    }

    async close() {
        try {
            if (this.page) await this.page.close();
            if (this.context) await this.context.close();
            if (this.browser) await this.browser.close();
            console.log('Browser closed successfully');
        } catch (error) {
            console.error('Error closing browser:', error);
        }
    }

    async run() {
        try {
            await this.init();
            const data = await this.fetchMarineTrafficData();
            await this.saveDataToFile(data);
            
            // Log summary
            if (data && data.data) {
                console.log(`\nSummary:`);
                console.log(`- Total vessels found: ${data.data.length}`);
                console.log(`- Data saved to: marine-traffic-data.json`);
            }
            
            return data;
        } catch (error) {
            console.error('Error in scraper run:', error);
            throw error;
        } finally {
            await this.close();
        }
    }
}

// Alternative method using direct API call
async function fetchWithDirectRequest() {
    const { chromium } = require('playwright');
    
    const browser = await chromium.launch({ headless: true });
    const context = await browser.newContext();
    
    // Load cookies
    const sessionData = await fs.readFile(path.join(__dirname, 'sessions', 'session.json'), 'utf8');
    const cookies = JSON.parse(sessionData);
    await context.addCookies(cookies);
    
    const page = await context.newPage();
    
    try {
        // Make API request using page.request
        const response = await page.request.get(
            'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in',
            {
                headers: {
                    'accept': 'application/json, text/plain, */*',
                    'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                    'if-modified-since': 'Tue, 19 Aug 2025 05:12:07 GMT',
                    'priority': 'u=1, i',
                    'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'x-requested-with': 'XMLHttpRequest'
                }
            }
        );
        
        if (response.ok()) {
            const data = await response.json();
            
            // Save data
            const outputPath = path.join(__dirname, 'marine-traffic-data-direct.json');
            await fs.writeFile(outputPath, JSON.stringify(data, null, 2), 'utf8');
            
            console.log('Data fetched and saved successfully using direct request method');
            console.log(`Data saved to: ${outputPath}`);
            
            return data;
        } else {
            throw new Error(`HTTP error! status: ${response.status()}`);
        }
        
    } finally {
        await browser.close();
    }
}

// Main execution
async function main() {
    console.log('Starting Marine Traffic Data Scraper...\n');
    
    try {
        // Method 1: Using class-based approach
        console.log('=== Method 1: Class-based approach ===');
        const scraper = new MarineTrafficScraper();
        await scraper.run();
        
        console.log('\n=== Method 2: Direct request approach ===');
        // Method 2: Using direct request
        await fetchWithDirectRequest();
        
    } catch (error) {
        console.error('Main execution error:', error);
        process.exit(1);
    }
}

// Export for use as module
module.exports = { MarineTrafficScraper, fetchWithDirectRequest };

// Run if called directly
if (require.main === module) {
    main();
}
