/**
 * CCTV Monitoring API Client - JavaScript/Node.js Example
 * ======================================================
 * 
 * This example demonstrates how to interact with the CCTV Monitoring API using JavaScript.
 */

const axios = require('axios');

class CCTVAPIClient {
    /**
     * Initialize the API client
     * @param {string} baseUrl - Base URL of the CCTV API
     * @param {string} apiKey - Your API key
     */
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiKey = apiKey;
        
        // Create axios instance with default headers
        this.client = axios.create({
            baseURL: this.baseUrl,
            headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        // Add response interceptor for error handling
        this.client.interceptors.response.use(
            response => response,
            error => {
                console.error('API Error:', error.response?.data || error.message);
                throw error;
            }
        );
    }
    
    // Stream Management Methods
    
    /**
     * List all active streams
     * @returns {Promise<Array>} Array of stream objects
     */
    async listStreams() {
        const response = await this.client.get('/streams');
        return response.data.data.streams;
    }
    
    /**
     * Create and start a new stream
     * @param {string} streamUrl - Stream URL
     * @param {string} name - Optional stream name
     * @returns {Promise<Object>} Created stream object
     */
    async createStream(streamUrl, name = null) {
        const data = { stream_url: streamUrl };
        if (name) data.name = name;
        
        const response = await this.client.post('/streams', data);
        return response.data.data;
    }
    
    /**
     * Get specific stream information
     * @param {string} streamId - Stream ID
     * @returns {Promise<Object>} Stream object
     */
    async getStream(streamId) {
        const response = await this.client.get(`/streams/${streamId}`);
        return response.data.data;
    }
    
    /**
     * Stop and delete a stream
     * @param {string} streamId - Stream ID
     * @returns {Promise<boolean>} Success status
     */
    async deleteStream(streamId) {
        const response = await this.client.delete(`/streams/${streamId}`);
        return response.data.success;
    }
    
    // Detection Methods
    
    /**
     * Get current detections for a stream
     * @param {string} streamId - Stream ID
     * @returns {Promise<Array>} Array of detection objects
     */
    async getDetections(streamId) {
        const response = await this.client.get(`/streams/${streamId}/detections`);
        return response.data.data.detections;
    }
    
    /**
     * Get detailed statistics for a stream
     * @param {string} streamId - Stream ID
     * @returns {Promise<Object>} Statistics object
     */
    async getStatistics(streamId) {
        const response = await this.client.get(`/streams/${streamId}/statistics`);
        return response.data.data;
    }
    
    /**
     * Reset statistics for a stream
     * @param {string} streamId - Stream ID
     * @returns {Promise<boolean>} Success status
     */
    async resetStatistics(streamId) {
        const response = await this.client.delete(`/streams/${streamId}/statistics`);
        return response.data.success;
    }
    
    // System Methods
    
    /**
     * Get overall system status
     * @returns {Promise<Object>} System status object
     */
    async getSystemStatus() {
        const response = await this.client.get('/system/status');
        return response.data.data;
    }
    
    /**
     * Simple health check
     * @returns {Promise<Object>} Health status
     */
    async healthCheck() {
        const response = await this.client.get('/system/health');
        return response.data;
    }
    
    // Webhook Methods
    
    /**
     * List registered webhooks
     * @returns {Promise<Array>} Array of webhook objects
     */
    async listWebhooks() {
        const response = await this.client.get('/webhooks');
        return response.data.data.webhooks;
    }
    
    /**
     * Register a webhook endpoint
     * @param {string} url - Webhook URL
     * @param {Array<string>} events - Array of event types
     * @returns {Promise<Object>} Webhook object
     */
    async registerWebhook(url, events) {
        const data = { url, events };
        const response = await this.client.post('/webhooks', data);
        return response.data.data;
    }
    
    /**
     * Delete a webhook
     * @param {string} webhookId - Webhook ID
     * @returns {Promise<boolean>} Success status
     */
    async deleteWebhook(webhookId) {
        const response = await this.client.delete(`/webhooks/${webhookId}`);
        return response.data.success;
    }
}

// Example Usage
async function main() {
    const client = new CCTVAPIClient(
        'http://localhost:5000/api/v1',
        'your-api-key-here'
    );
    
    try {
        // Health check
        console.log('🔍 Checking system health...');
        const health = await client.healthCheck();
        console.log(`✅ System status: ${health.status}`);
        
        // Get system status
        console.log('\n📊 Getting system status...');
        const status = await client.getSystemStatus();
        console.log(`📈 Active streams: ${status.streams.active}`);
        console.log(`🚗 Total detections: ${status.detections.total_detections}`);
        
        // Create a new stream
        console.log('\n🎥 Creating new stream...');
        const streamUrl = "http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8";
        const stream = await client.createStream(streamUrl, "Test Stream");
        const streamId = stream.stream_id;
        console.log(`✅ Stream created: ${streamId}`);
        
        // Wait for stream to start processing
        console.log('\n⏳ Waiting for stream to process...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Get detections
        console.log('\n🔍 Getting current detections...');
        const detections = await client.getDetections(streamId);
        console.log(`🚗 Current detections: ${detections.length}`);
        
        detections.slice(0, 3).forEach(detection => {
            console.log(`  - ${detection.vehicle_type}: confidence ${detection.confidence.toFixed(2)}`);
        });
        
        // Get statistics
        console.log('\n📊 Getting stream statistics...');
        const stats = await client.getStatistics(streamId);
        console.log(`📈 Frame count: ${stats.frame_count}`);
        console.log(`🚗 Unique vehicles: ${stats.tracking_stats.total_unique}`);
        
        // Register webhook (optional)
        console.log('\n🔗 Registering webhook...');
        try {
            const webhook = await client.registerWebhook(
                "https://your-server.com/webhook",
                ["vehicle_detected", "unique_vehicle_counted"]
            );
            console.log(`✅ Webhook registered: ${webhook.webhook_id}`);
        } catch (error) {
            console.log(`⚠️ Webhook registration failed: ${error.message}`);
        }
        
        // List all streams
        console.log('\n📋 Listing all streams...');
        const streams = await client.listStreams();
        streams.forEach(stream => {
            console.log(`  - ${stream.stream_id}: ${stream.stream_url.substring(0, 50)}...`);
        });
        
        // Clean up - delete the stream
        console.log(`\n🗑️ Cleaning up - deleting stream ${streamId}...`);
        await client.deleteStream(streamId);
        console.log('✅ Stream deleted');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Utility Functions

/**
 * Monitor a stream continuously for a specified duration
 * @param {CCTVAPIClient} client - API client instance
 * @param {string} streamId - Stream ID to monitor
 * @param {number} duration - Duration in seconds
 */
async function monitorStreamContinuously(client, streamId, duration = 60) {
    console.log(`🔍 Monitoring stream ${streamId} for ${duration} seconds...`);
    
    const startTime = Date.now();
    let lastDetectionCount = 0;
    
    while (Date.now() - startTime < duration * 1000) {
        try {
            const detections = await client.getDetections(streamId);
            const currentCount = detections.length;
            
            if (currentCount !== lastDetectionCount) {
                console.log(`🚗 Detection count changed: ${currentCount}`);
                lastDetectionCount = currentCount;
            }
            
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
            
        } catch (error) {
            console.error(`❌ Monitoring error: ${error.message}`);
            break;
        }
    }
    
    console.log('✅ Monitoring completed');
}

/**
 * Create multiple streams in batch
 * @param {CCTVAPIClient} client - API client instance
 * @param {Array<string>} streamUrls - Array of stream URLs
 * @returns {Promise<Array<string>>} Array of created stream IDs
 */
async function batchCreateStreams(client, streamUrls) {
    const streamIds = [];
    
    for (let i = 0; i < streamUrls.length; i++) {
        try {
            const stream = await client.createStream(streamUrls[i], `Batch Stream ${i + 1}`);
            streamIds.push(stream.stream_id);
            console.log(`✅ Created stream ${i + 1}: ${stream.stream_id}`);
        } catch (error) {
            console.error(`❌ Failed to create stream ${i + 1}: ${error.message}`);
        }
    }
    
    return streamIds;
}

/**
 * Export statistics to JSON file
 * @param {CCTVAPIClient} client - API client instance
 * @param {string} streamId - Stream ID
 * @param {string} filename - Output filename
 */
async function exportStatisticsToJSON(client, streamId, filename) {
    const fs = require('fs').promises;
    
    try {
        const stats = await client.getStatistics(streamId);
        
        const exportData = {
            timestamp: new Date().toISOString(),
            stream_id: streamId,
            frame_count: stats.frame_count,
            total_detections: stats.detection_stats.total_detections,
            unique_vehicles: stats.tracking_stats.total_unique,
            vehicle_breakdown: stats.tracking_stats.by_type
        };
        
        await fs.writeFile(filename, JSON.stringify(exportData, null, 2));
        console.log(`✅ Statistics exported to ${filename}`);
        
    } catch (error) {
        console.error(`❌ Export failed: ${error.message}`);
    }
}

/**
 * Real-time event listener using webhooks
 * @param {number} port - Port to listen on
 */
function startWebhookListener(port = 3000) {
    const express = require('express');
    const app = express();
    
    app.use(express.json());
    
    app.post('/webhook', (req, res) => {
        const { event_type, timestamp, data } = req.body;
        
        console.log(`🔔 Webhook received: ${event_type} at ${timestamp}`);
        
        switch (event_type) {
            case 'vehicle_detected':
                console.log(`🚗 Vehicle detected: ${data.vehicle_type} (confidence: ${data.confidence})`);
                break;
            case 'unique_vehicle_counted':
                console.log(`📊 Unique vehicle counted: ${data.vehicle_type} (total: ${data.total_unique_count})`);
                break;
            case 'stream_started':
                console.log(`🎥 Stream started: ${data.stream_id}`);
                break;
            case 'stream_stopped':
                console.log(`⏹️ Stream stopped: ${data.stream_id}`);
                break;
        }
        
        res.status(200).json({ received: true });
    });
    
    app.listen(port, () => {
        console.log(`🔗 Webhook listener started on port ${port}`);
        console.log(`📡 Webhook URL: http://localhost:${port}/webhook`);
    });
}

// Export for use as module
module.exports = {
    CCTVAPIClient,
    monitorStreamContinuously,
    batchCreateStreams,
    exportStatisticsToJSON,
    startWebhookListener
};

// Run example if called directly
if (require.main === module) {
    main();
}
