"""
Simple MySQL helper for persisting stream metadata
- Uses PyMySQL (pure-Python) for easy installation
- Reads credentials from environment, with provided defaults

Env vars (optional):
  DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT
"""
from __future__ import annotations
import os
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# DB configuration (defaults based on user's provided credentials)
DB_HOST = os.environ.get("DB_HOST", "***********")
DB_USER = os.environ.get("DB_USER", "root")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "owr216he890")
DB_NAME = os.environ.get("DB_NAME", "cctv")
DB_PORT = int(os.environ.get("DB_PORT", "3306"))

# Lazy import so codebase works even if dependency not yet installed
try:
    import pymysql
    from pymysql.cursors import DictCursor
except Exception as e:  # pragma: no cover - import error handling
    pymysql = None
    DictCursor = None
    logger.warning(
        "PyMySQL not installed. Install with: pip install PyMySQL. "
        "Database features will be disabled until installed."
    )

_connection: Optional["pymysql.Connection"] = None

def get_connection() -> Optional["pymysql.Connection"]:
    """Get (and memoize) a DB connection. Returns None if unavailable."""
    global _connection
    if pymysql is None:
        return None
    try:
        if _connection is None or not _connection.open:
            _connection = pymysql.connect(
                host=DB_HOST,
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME,
                port=DB_PORT,
                autocommit=True,
                cursorclass=DictCursor,
                charset="utf8mb4",
                connect_timeout=3,
                read_timeout=5,
                write_timeout=5,
            )
        else:
            try:
                _connection.ping(reconnect=True)
            except Exception:
                _connection = pymysql.connect(
                    host=DB_HOST,
                    user=DB_USER,
                    password=DB_PASSWORD,
                    database=DB_NAME,
                    port=DB_PORT,
                    autocommit=True,
                    cursorclass=DictCursor,
                    charset="utf8mb4",
                    connect_timeout=3,
                    read_timeout=5,
                    write_timeout=5,
                )
        return _connection
    except Exception as e:
        logger.error(f"Failed to connect to MySQL at {DB_HOST}:{DB_PORT} db={DB_NAME}: {e}")
        return None


def init_db() -> bool:
    """Create required tables if not exist. Safe to call multiple times."""
    conn = get_connection()
    if conn is None:
        logger.warning("MySQL not available; skipping DB initialization")
        return False
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                CREATE TABLE IF NOT EXISTS streams (
                  id VARCHAR(64) PRIMARY KEY,
                  stream_url TEXT NOT NULL,
                  name VARCHAR(255) NULL,
                  status VARCHAR(32) NOT NULL DEFAULT 'active',
                  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """
            )
        logger.info("Database initialized (streams table ready)")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


def save_stream(stream_id: str, stream_url: str, name: Optional[str], status: str = "active") -> bool:
    """Insert or update a stream row."""
    conn = get_connection()
    if conn is None:
        return False
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO streams (id, stream_url, name, status)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                  stream_url = VALUES(stream_url),
                  name = VALUES(name),
                  status = VALUES(status)
                """,
                (stream_id, stream_url, name, status),
            )
        return True
    except Exception as e:
        logger.error(f"Failed to save stream {stream_id}: {e}")
        return False


def update_stream_status(stream_id: str, status: str) -> bool:
    """Update status for a stream (e.g., 'stopped', 'deleted')."""
    conn = get_connection()
    if conn is None:
        return False
    try:
        with conn.cursor() as cur:
            cur.execute(
                "UPDATE streams SET status=%s WHERE id=%s",
                (status, stream_id),
            )
        return True
    except Exception as e:
        logger.error(f"Failed to update status for stream {stream_id}: {e}")
        return False


def get_stream(stream_id: str) -> Optional[Dict[str, Any]]:
    """Fetch a stream by id."""
    conn = get_connection()
    if conn is None:
        return None
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT * FROM streams WHERE id=%s", (stream_id,))
            return cur.fetchone()
    except Exception as e:
        logger.error(f"Failed to fetch stream {stream_id}: {e}")
        return None


def list_streams(limit: int = 100) -> list[Dict[str, Any]]:
    conn = get_connection()
    if conn is None:
        return []
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT * FROM streams ORDER BY updated_at DESC LIMIT %s", (limit,))
            return list(cur.fetchall())
    except Exception as e:
        logger.error(f"Failed to list streams: {e}")
        return []

