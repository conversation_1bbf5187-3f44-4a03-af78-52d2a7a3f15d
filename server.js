const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const { CompleteMarineTrafficScraper } = require('./marine-scraper-complete');
const { MarineTrafficScheduler } = require('./scheduler');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Global variables
let isScrapingInProgress = false;
let lastScrapingResult = null;
let scheduler = null;

/**
 * Helper function to find latest data file
 */
async function findLatestDataFile(pattern = 'marine-traffic-') {
    try {
        const files = await fs.readdir(__dirname);
        const dataFiles = files
            .filter(file => file.startsWith(pattern) && file.endsWith('.json'))
            .map(file => ({
                name: file,
                path: path.join(__dirname, file),
                timestamp: parseInt(file.match(/(\d+)\.json$/)?.[1] || '0')
            }))
            .sort((a, b) => b.timestamp - a.timestamp);

        return dataFiles.length > 0 ? dataFiles[0] : null;
    } catch (error) {
        console.error('Error finding latest data file:', error);
        return null;
    }
}

/**
 * Helper function to load data from file
 */
async function loadDataFromFile(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading data from file:', error);
        return null;
    }
}

/**
 * Helper function to generate statistics
 */
function generateStatistics(vessels) {
    const stats = {
        total: vessels.length,
        countries: {},
        shipTypes: {},
        ports: {},
        indonesianVessels: 0,
        foreignVessels: 0
    };

    vessels.forEach(vessel => {
        // Country statistics
        const country = vessel.COUNTRY || 'Unknown';
        stats.countries[country] = (stats.countries[country] || 0) + 1;

        if (country === 'Indonesia') {
            stats.indonesianVessels++;
        } else {
            stats.foreignVessels++;
        }

        // Ship type statistics
        const type = vessel.TYPE_SUMMARY || 'Unknown';
        stats.shipTypes[type] = (stats.shipTypes[type] || 0) + 1;

        // Port statistics
        const port = vessel.CURRENT_PORT || 'Unknown';
        stats.ports[port] = (stats.ports[port] || 0) + 1;
    });

    return {
        ...stats,
        topCountries: Object.entries(stats.countries).sort(([, a], [, b]) => b - a).slice(0, 10),
        topShipTypes: Object.entries(stats.shipTypes).sort(([, a], [, b]) => b - a),
        topPorts: Object.entries(stats.ports).sort(([, a], [, b]) => b - a).slice(0, 20)
    };
}

// Routes

/**
 * GET / - API Documentation
 */
app.get('/', (req, res) => {
    res.json({
        name: 'Marine Traffic API',
        version: '1.0.0',
        description: 'API untuk mengakses data kapal dari MarineTraffic Indonesia',
        endpoints: {
            'GET /': 'API documentation',
            'GET /api/vessels': 'Get all vessels data',
            'GET /api/vessels/stats': 'Get vessels statistics',
            'GET /api/vessels/search': 'Search vessels (query params: name, country, type, port)',
            'GET /api/vessels/:id': 'Get vessel by SHIP_ID',
            'POST /api/scrape': 'Start new scraping process',
            'GET /api/scrape/status': 'Get scraping status',
            'POST /api/scheduler/start': 'Start automatic scheduler',
            'POST /api/scheduler/stop': 'Stop automatic scheduler',
            'GET /api/scheduler/status': 'Get scheduler status',
            'GET /api/files': 'List available data files',
            'GET /api/health': 'Health check'
        },
        examples: {
            'Get all vessels': 'GET /api/vessels',
            'Search by name': 'GET /api/vessels/search?name=SAGA',
            'Search by country': 'GET /api/vessels/search?country=Indonesia',
            'Search by type': 'GET /api/vessels/search?type=Cargo',
            'Get vessel by ID': 'GET /api/vessels/1283618'
        }
    });
});

/**
 * GET /api/health - Health check
 */
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

/**
 * GET /api/vessels - Get all vessels
 */
app.get('/api/vessels', async (req, res) => {
    try {
        const latestFile = await findLatestDataFile();
        if (!latestFile) {
            return res.status(404).json({
                error: 'No data available',
                message: 'Please run scraping first using POST /api/scrape'
            });
        }

        const data = await loadDataFromFile(latestFile.path);
        if (!data) {
            return res.status(500).json({
                error: 'Failed to load data',
                message: 'Data file exists but cannot be read'
            });
        }

        res.json({
            success: true,
            timestamp: new Date(latestFile.timestamp).toISOString(),
            total: data.data.length,
            totalCount: data.totalCount || data.data.length,
            note: "This is ALL available unique vessels from MarineTraffic for Indonesian waters. Data collected through aggressive pagination (25,000+ raw records) and cleaned for duplicates.",
            dataSource: "MarineTraffic.com",
            coverage: "All vessels in Indonesian waters",
            lastUpdated: new Date(latestFile.timestamp).toISOString(),
            data: data.data
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * GET /api/vessels/stats - Get vessels statistics
 */
app.get('/api/vessels/stats', async (req, res) => {
    try {
        const latestFile = await findLatestDataFile();
        if (!latestFile) {
            return res.status(404).json({
                error: 'No data available',
                message: 'Please run scraping first using POST /api/scrape'
            });
        }

        const data = await loadDataFromFile(latestFile.path);
        if (!data) {
            return res.status(500).json({
                error: 'Failed to load data'
            });
        }

        const stats = generateStatistics(data.data);

        res.json({
            success: true,
            timestamp: new Date(latestFile.timestamp).toISOString(),
            dataInfo: {
                totalUniqueVessels: stats.total,
                dataSource: "MarineTraffic.com",
                coverage: "All vessels in Indonesian waters",
                collectionMethod: "Aggressive pagination with duplicate removal",
                note: "This represents ALL available unique vessels from MarineTraffic database for Indonesian waters"
            },
            statistics: stats
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * GET /api/vessels/search - Search vessels
 */
app.get('/api/vessels/search', async (req, res) => {
    try {
        const { name, country, type, port, imo, mmsi } = req.query;

        const latestFile = await findLatestDataFile();
        if (!latestFile) {
            return res.status(404).json({
                error: 'No data available'
            });
        }

        const data = await loadDataFromFile(latestFile.path);
        if (!data) {
            return res.status(500).json({
                error: 'Failed to load data'
            });
        }

        let filteredVessels = data.data;

        // Apply filters
        if (name) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.SHIPNAME && vessel.SHIPNAME.toLowerCase().includes(name.toLowerCase())
            );
        }

        if (country) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.COUNTRY && vessel.COUNTRY.toLowerCase().includes(country.toLowerCase())
            );
        }

        if (type) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.TYPE_SUMMARY && vessel.TYPE_SUMMARY.toLowerCase().includes(type.toLowerCase())
            );
        }

        if (port) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.CURRENT_PORT && vessel.CURRENT_PORT.toLowerCase().includes(port.toLowerCase())
            );
        }

        if (imo) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.IMO && vessel.IMO.includes(imo)
            );
        }

        if (mmsi) {
            filteredVessels = filteredVessels.filter(vessel =>
                vessel.MMSI && vessel.MMSI.includes(mmsi)
            );
        }

        res.json({
            success: true,
            query: req.query,
            total: filteredVessels.length,
            totalAvailable: data.data.length,
            note: "All matching vessels returned. No pagination limits applied.",
            data: filteredVessels
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * GET /api/vessels/:id - Get vessel by SHIP_ID
 */
app.get('/api/vessels/:id', async (req, res) => {
    try {
        const shipId = req.params.id;

        const latestFile = await findLatestDataFile();
        if (!latestFile) {
            return res.status(404).json({
                error: 'No data available'
            });
        }

        const data = await loadDataFromFile(latestFile.path);
        if (!data) {
            return res.status(500).json({
                error: 'Failed to load data'
            });
        }

        const vessel = data.data.find(v => v.SHIP_ID === shipId);

        if (!vessel) {
            return res.status(404).json({
                error: 'Vessel not found',
                message: `No vessel found with SHIP_ID: ${shipId}`
            });
        }

        res.json({
            success: true,
            data: vessel
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * POST /api/scrape - Start new scraping
 */
app.post('/api/scrape', async (req, res) => {
    if (isScrapingInProgress) {
        return res.status(409).json({
            error: 'Scraping already in progress',
            message: 'Please wait for current scraping to complete'
        });
    }

    try {
        isScrapingInProgress = true;

        res.json({
            success: true,
            message: 'Scraping started',
            status: 'in_progress'
        });

        // Start scraping in background
        const scraper = new CompleteMarineTrafficScraper({
            headless: true,
            outputPath: path.join(__dirname, `marine-traffic-complete-${Date.now()}.json`)
        });

        const result = await scraper.run();
        lastScrapingResult = {
            success: true,
            timestamp: new Date().toISOString(),
            vesselsCount: result.data.data.length,
            outputPath: result.outputPath
        };

    } catch (error) {
        lastScrapingResult = {
            success: false,
            timestamp: new Date().toISOString(),
            error: error.message
        };
    } finally {
        isScrapingInProgress = false;
    }
});

/**
 * GET /api/scrape/status - Get scraping status
 */
app.get('/api/scrape/status', (req, res) => {
    res.json({
        isInProgress: isScrapingInProgress,
        lastResult: lastScrapingResult
    });
});

/**
 * GET /api/files - List available data files
 */
app.get('/api/files', async (req, res) => {
    try {
        const files = await fs.readdir(__dirname);
        const dataFiles = files
            .filter(file => file.startsWith('marine-traffic-') && file.endsWith('.json'))
            .map(file => {
                const stats = require('fs').statSync(path.join(__dirname, file));
                const timestamp = file.match(/(\d+)\.json$/)?.[1];
                return {
                    name: file,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime,
                    timestamp: timestamp ? new Date(parseInt(timestamp)).toISOString() : null
                };
            })
            .sort((a, b) => new Date(b.modified) - new Date(a.modified));

        res.json({
            success: true,
            total: dataFiles.length,
            files: dataFiles
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🌊 Marine Traffic API Server running on port ${PORT}`);
    console.log(`📖 API Documentation: http://localhost:${PORT}`);
    console.log(`🚢 Vessels API: http://localhost:${PORT}/api/vessels`);
    console.log(`📊 Statistics API: http://localhost:${PORT}/api/vessels/stats`);
    console.log(`🔍 Search API: http://localhost:${PORT}/api/vessels/search?name=SAGA`);
});

module.exports = app;
