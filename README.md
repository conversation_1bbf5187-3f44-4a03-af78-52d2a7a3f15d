# Marine Traffic Data Scraper

Script untuk mengambil data kapal dari MarineTraffic.com menggunakan Playwright dengan session yang sudah ada.

## Fitur

- ✅ Menggunakan session cookies yang sudah tersimpan
- ✅ Mengambil data kapal di pelabuhan Indonesia
- ✅ Menyimpan data dalam format JSON
- ✅ Dua metode scraping (page navigation dan direct request)
- ✅ Error handling dan logging yang detail

## Prerequisites

1. Node.js (versi 14 atau lebih baru)
2. Playwright browsers
3. File session.json yang valid di folder `sessions/`

## Instalasi

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npm run install-browsers
```

## Penggunaan

### Method 1: API Server - Akses Data via REST API ⭐⭐⭐
```bash
npm start
```
**Menjalankan server Express di port 3000 dengan REST API lengkap untuk akses data**
- 🌐 **Web Interface**: http://localhost:3000
- 📊 **API Endpoints**: http://localhost:3000/api/vessels
- 🔍 **Search API**: http://localhost:3000/api/vessels/search
- 📈 **Statistics**: http://localhost:3000/api/vessels/stats

### Method 2: Complete Scraper - SEMUA DATA (Recommended) ⭐
```bash
npm run scrape-complete
```
**Mengambil SEMUA data kapal dengan pagination, menghilangkan duplikasi, dan menghasilkan format yang sama persis dengan download.json**

### Method 2: Full Scraper - Data mentah dengan pagination
```bash
npm run scrape-full
```

### Method 3: Scraper Produksi - 500 data terproses
```bash
npm run scrape-prod
```

### Method 4: Scheduler - Jalankan sekali
```bash
npm run scrape-once
```

### Method 5: Scheduler - Jalankan otomatis setiap 30 menit
```bash
npm run scheduler
```

### Method 6: Test scraper (dengan logging detail)
```bash
npm run test-scrape
```

### Method 7: Menjalankan langsung
```bash
node marine-scraper-complete.js    # SEMUA data
node marine-scraper-full.js        # Data mentah
node marine-scraper-production.js  # Data terproses
node scheduler.js once
node scheduler.js start
```

### Method 8: Membersihkan data lama
```bash
npm run clean-data
# atau dengan jumlah file yang ingin disimpan
node scheduler.js clean 5
```

## File Output

Script akan menghasilkan file-file berikut:

### Scraper Produksi:
- `marine-traffic-[timestamp].json` - Data hasil scraping dengan format yang sudah diproses
- `data/marine-traffic-[timestamp].json` - Data dari scheduler (disimpan di folder data/)

### Test Scraper:
- `marine-traffic-data.json` - Data hasil scraping mentah
- `marine-traffic-data-direct.json` - Data hasil direct request
- `debug-response.html` - File debug jika terjadi error parsing JSON

### Scheduler:
- `scraper.log` - Log file dari scheduler
- `data/` - Folder berisi semua data hasil scraping terjadwal

## Struktur Data

Data yang diambil mencakup kolom-kolom berikut:
- `flag` - Bendera kapal
- `shipname` - Nama kapal
- `photo` - Foto kapal
- `recognized_next_port` - Pelabuhan tujuan berikutnya
- `reported_eta` - Estimasi waktu tiba
- `reported_destination` - Tujuan yang dilaporkan
- `current_port` - Pelabuhan saat ini
- `imo` - Nomor IMO
- `ship_type` - Jenis kapal
- `show_on_live_map` - Status tampil di live map
- `time_of_latest_position` - Waktu posisi terakhir
- `lat_of_latest_position` - Latitude posisi terakhir
- `lon_of_latest_position` - Longitude posisi terakhir
- `notes` - Catatan

## Filter

Script ini menggunakan filter:
- `current_port_country_in=ID` - Kapal di pelabuhan Indonesia
- `asset_type=vessels` - Hanya kapal

## Session Management

File `sessions/session.json` harus berisi cookies dalam format array JSON:

```json
[
  {
    "name": "cookie_name",
    "value": "cookie_value",
    "domain": ".marinetraffic.com",
    "path": "/",
    "expires": -1,
    "httpOnly": false,
    "secure": false
  }
]
```

## Troubleshooting

### Error: Session file not found
Pastikan file `sessions/session.json` ada dan berisi cookies yang valid.

### Error: HTTP 403/401
Session cookies mungkin sudah expired. Perbarui file session.json dengan cookies yang baru.

### Error: Timeout
Coba tingkatkan timeout atau periksa koneksi internet.

### Error: JSON parsing failed
Periksa file `debug-response.html` untuk melihat response yang diterima.

## Konfigurasi

### Browser Settings
- Headless mode: Set `headless: true` untuk production
- User Agent: Menggunakan Chrome 139 Windows
- Viewport: 1920x1080

### Request Headers
Script menggunakan headers yang sesuai dengan request curl asli:
- Accept: application/json
- User-Agent: Chrome 139
- Referer: MarineTraffic data page
- Security headers (sec-ch-ua, sec-fetch-*)

## Contoh Output

### Format Data Produksi (Processed):
```json
{
  "timestamp": "2025-08-19T05:24:17.047Z",
  "total_vessels": 500,
  "vessels": [
    {
      "ship_id": "1283618",
      "imo": "7911545",
      "mmsi": "210121000",
      "callsign": "5BRC5",
      "shipname": "SAGA",
      "country": "Cyprus",
      "flag": "CY",
      "ship_type": "Passenger",
      "current_port": "BATU AMPAR",
      "current_port_country": "ID",
      "next_port": "BATU AMPAR",
      "next_port_country": "ID",
      "destination": "BATAM SHIPYARD",
      "eta": "2025-07-29T05:30:00.000Z",
      "last_position_time": "2025-08-19T05:18:52.000Z",
      "latitude": 1.0983417,
      "longitude": 103.89544,
      "speed": 0,
      "course": 173,
      "photos_count": 215
    }
  ]
}
```

### Format Data Mentah (Raw):
```json
{
  "data": [
    {
      "SHIP_ID": "1283618",
      "IMO": "7911545",
      "SHIPNAME": "SAGA",
      "CURRENT_PORT": "BATU AMPAR",
      "TYPE_SUMMARY": "Passenger",
      "LAT": "1.0983417",
      "LON": "103.89544"
    }
  ],
  "totalCount": 500
}
```

## API Server

### Fitur API Server:
- ✅ **REST API lengkap** untuk akses data kapal
- ✅ **Web interface** dengan dokumentasi interaktif
- ✅ **Search & Filter** berdasarkan nama, negara, jenis, pelabuhan
- ✅ **Real-time statistics** dan monitoring
- ✅ **CORS enabled** untuk akses dari browser
- ✅ **Background scraping** via API endpoint
- ✅ **Multiple data formats** (JSON, statistik, raw data)

### API Endpoints:
```
GET  /api/vessels           - Semua data kapal
GET  /api/vessels/stats     - Statistik lengkap
GET  /api/vessels/search    - Pencarian dengan filter
GET  /api/vessels/:id       - Data kapal berdasarkan ID
POST /api/scrape           - Mulai scraping baru
GET  /api/scrape/status    - Status scraping
GET  /api/files            - Daftar file data
GET  /api/health           - Health check
```

### Menjalankan Server:
```bash
npm start                  # Start server di port 3000
npm run dev               # Development mode dengan nodemon
```

### Akses Server:
- **Web Interface**: http://localhost:3000
- **API Documentation**: http://localhost:3000
- **All Vessels**: http://localhost:3000/api/vessels
- **Statistics**: http://localhost:3000/api/vessels/stats
- **Search Indonesian Ships**: http://localhost:3000/api/vessels/search?country=Indonesia

## Scheduler

### Fitur Scheduler:
- ✅ Menjalankan scraping otomatis setiap 30 menit
- ✅ Retry otomatis jika gagal (maksimal 3 kali)
- ✅ Logging lengkap ke file dan console
- ✅ Pembersihan data lama otomatis
- ✅ Graceful shutdown dengan Ctrl+C

### Konfigurasi Scheduler:
```javascript
const scheduler = new MarineTrafficScheduler({
    interval: 30 * 60 * 1000,    // 30 menit
    maxRetries: 3,               // Maksimal retry
    retryDelay: 5 * 60 * 1000,   // Delay retry 5 menit
    outputDir: './data',         // Folder output
    logFile: './scraper.log'     // File log
});
```

### Menjalankan Scheduler:
```bash
# Jalankan scheduler (terus menerus)
npm run scheduler

# Jalankan sekali saja
npm run scrape-once

# Bersihkan data lama (simpan 10 file terakhir)
npm run clean-data

# Bersihkan data lama (simpan 5 file terakhir)
node scheduler.js clean 5
```

## Catatan Penting

1. **Rate Limiting**: Jangan menjalankan script terlalu sering untuk menghindari rate limiting
2. **Session Expiry**: Cookies session akan expired, perlu diperbarui secara berkala
3. **Legal**: Pastikan penggunaan sesuai dengan Terms of Service MarineTraffic
4. **Data Accuracy**: Data yang diperoleh sesuai dengan yang tersedia di MarineTraffic pada saat request
5. **Monitoring**: Periksa file `scraper.log` untuk monitoring status scheduler

## Lisensi

ISC License
