# CCTV Serverless Permission & Compatibility Fix

## Problem Description

When deploying the CCTV monitoring system to serverless environments (Google Cloud Run, AWS Fargate, etc.), you may encounter these errors:

### 1. Permission Error (FIXED)
```
ERROR:app:Error loading YOLOv5 model: [<PERSON><PERSON><PERSON> 13] Permission denied: '/home/<USER>'
```

### 2. Compatibility Error (FIXED)
```
ERROR:app:Error loading YOLOv5 model: cannot import name 'torch_load' from 'ultralytics.utils.patches'
```

These errors occur when accessing the `/start_stream` endpoint due to:
1. PyTorch's `torch.hub` trying to cache models in a non-writable directory
2. Version conflicts between YOLOv5 and ultralytics packages

## Root Cause

### Permission Issue
1. **PyTorch Hub Cache**: `torch.hub.load()` downloads and caches models in the user's home directory by default
2. **Container Security**: The Docker container runs as a non-root user (`appuser`) for security
3. **Permission Mismatch**: The `appuser` home directory (`/home/<USER>

### Compatibility Issue
1. **Version Conflict**: The ultralytics package version conflicts with YOLOv5 repository code
2. **Import Error**: Missing or incompatible `torch_load` function in ultralytics.utils.patches
3. **API Changes**: Different model loading APIs between torch.hub and ultralytics

## Solution

The fix involves two main components:

### 1. Permission Fix
Redirect PyTorch's cache directory to a writable location within the container:
- `TORCH_HOME=/app/.cache/torch` - PyTorch cache directory
- `XDG_CACHE_HOME=/app/.cache` - XDG cache directory (fallback)

### 2. Compatibility Fix
Implement multiple model loading strategies with fallbacks:
1. **Primary**: Use ultralytics YOLO (more stable)
2. **Fallback**: Use torch.hub with force_reload and trust_repo
3. **Detection**: Handle both ultralytics and torch.hub result formats

## Files Modified

### 1. Dockerfile
- Added creation of `/app/.cache/torch/hub` directory
- Set `TORCH_HOME` and `XDG_CACHE_HOME` environment variables in both application and serverless stages

### 2. app.py
- Added environment variable setup at the top of the file before importing torch

### 3. serverless_start.py
- Added environment variable setup before importing the Flask app

### 4. serverless_handler.py
- Added environment variable setup in the serverless handler

### 5. SERVERLESS_DEPLOYMENT_GUIDE.md
- Updated deployment commands to include the new environment variables
- Added troubleshooting section for permission errors

## Quick Fix for Existing Deployments

### Option 1: Use the Fix Script
```bash
# Run the automated fix script
./fix_serverless_permissions.sh
```

### Option 2: Manual Fix for Google Cloud Run
```bash
# Rebuild with fixes
docker build --target serverless -t cctv-monitoring-serverless .

# Tag for GCR
docker tag cctv-monitoring-serverless gcr.io/YOUR_PROJECT_ID/cctv-monitoring:fixed

# Push to registry
docker push gcr.io/YOUR_PROJECT_ID/cctv-monitoring:fixed

# Update Cloud Run service
gcloud run deploy cctv-monitoring \
  --image gcr.io/YOUR_PROJECT_ID/cctv-monitoring:fixed \
  --region asia-southeast2 \
  --set-env-vars SERVERLESS=true,FLASK_ENV=production,FLASK_DEBUG=0,TORCH_HOME=/app/.cache/torch,XDG_CACHE_HOME=/app/.cache,CCTV_ADMIN_KEY=cctv_admin_prod_2024
```

### Option 3: Environment Variables Only (Temporary Fix)
If you can't rebuild the image immediately, you can try setting just the environment variables:

```bash
gcloud run services update cctv-monitoring \
  --region asia-southeast2 \
  --set-env-vars TORCH_HOME=/app/.cache/torch,XDG_CACHE_HOME=/app/.cache,CCTV_ADMIN_KEY=cctv_admin_prod_2024
```

## Testing the Fix

### Local Testing
```bash
# Test the fix locally
python test_torch_cache.py

# Or test in Docker
docker run --rm cctv-monitoring-serverless python test_torch_cache.py
```

### Production Testing
```bash
# Test health endpoint
curl -f https://your-service-url/api/v1/system/health

# Test stream endpoint (should not give permission error)
curl -X POST https://your-service-url/start_stream \
  -H "Content-Type: application/json" \
  -d '{"stream_url": "test"}'
```

## Admin Key Configuration

The serverless deployment now includes a static admin key for consistent API access:

```
Production Admin Key: cctv_admin_prod_2024
```

### Testing Admin Access
```bash
# Test with the static admin key
curl -H "X-API-Key: cctv_admin_prod_2024" \
     https://your-service-url.com/api/v1/system/health

# List API keys (admin only)
curl -H "X-API-Key: cctv_admin_prod_2024" \
     https://your-service-url.com/api/v1/auth/keys
```

### Custom Admin Key
To use a custom admin key, update the environment variable:
```bash
gcloud run services update cctv-monitoring \
  --region asia-southeast2 \
  --set-env-vars CCTV_ADMIN_KEY=your_custom_admin_key
```

## Verification

After applying the fix, you should see:

1. ✅ No more "Permission denied: '/home/<USER>'" errors
2. ✅ YOLOv5 models load successfully
3. ✅ `/start_stream` endpoint works without errors
4. ✅ Container logs show successful model loading
5. ✅ Admin API key works consistently

## Prevention

To prevent this issue in future deployments:

1. Always use the `serverless` target when building Docker images for serverless deployment
2. Ensure environment variables are set in your deployment configuration
3. Test locally with the serverless configuration before deploying

## Additional Notes

- **Memory Requirements**: Ensure your serverless service has at least 2GB of memory allocated for YOLOv5 models
- **Cold Start**: First model loading may take longer due to downloading, subsequent requests will be faster
- **Model Caching**: Models are now cached in `/app/.cache/torch/hub` within the container
- **Security**: The fix maintains security by keeping the non-root user while providing necessary write permissions

## Support

If you continue to experience issues after applying this fix:

1. Check container logs for detailed error messages
2. Verify memory allocation (minimum 2GB recommended)
3. Ensure internet connectivity for initial model downloads
4. Test with the provided test script: `python test_torch_cache.py`
