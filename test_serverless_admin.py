#!/usr/bin/env python3
"""
Test script untuk admin key di serverless deployment
"""

import requests
import json
import sys
import os
import time

def test_serverless_admin(service_url, admin_key):
    """Test admin key functionality di serverless environment"""
    
    print("🌐 Testing Serverless Admin Key")
    print("=" * 35)
    print(f"Service URL: {service_url}")
    print(f"Admin Key: {admin_key}")
    print()
    
    headers = {
        'X-API-Key': admin_key,
        'Content-Type': 'application/json'
    }
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Health Check
    total_tests += 1
    print("🧪 Test 1: Health Check")
    try:
        response = requests.get(f"{service_url}/api/v1/system/health", 
                              headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ PASSED - Status: {data.get('status', 'unknown')}")
            tests_passed += 1
        else:
            print(f"   ❌ FAILED - HTTP {response.status_code}")
            print(f"   Response: {response.text[:100]}...")
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
    
    # Test 2: System Stats (Admin access required)
    total_tests += 1
    print("\n🧪 Test 2: System Stats (Admin Access)")
    try:
        response = requests.get(f"{service_url}/api/v1/system/stats", 
                              headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ PASSED - Admin access confirmed")
                tests_passed += 1
            else:
                print(f"   ❌ FAILED - {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ FAILED - HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
    
    # Test 3: List API Keys (Admin only)
    total_tests += 1
    print("\n🧪 Test 3: List API Keys (Admin Only)")
    try:
        response = requests.get(f"{service_url}/api/v1/auth/keys", 
                              headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                keys_count = len(data.get('data', {}).get('api_keys', []))
                print(f"   ✅ PASSED - Found {keys_count} API keys")
                tests_passed += 1
            else:
                print(f"   ❌ FAILED - {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ FAILED - HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
    
    # Test 4: Create Test Stream
    total_tests += 1
    print("\n🧪 Test 4: Create Test Stream")
    try:
        stream_data = {
            "stream_url": "test://dummy-stream-for-testing",
            "name": "Test Stream"
        }
        response = requests.post(f"{service_url}/api/v1/streams", 
                               headers=headers, json=stream_data, timeout=15)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stream_id = data.get('data', {}).get('stream_id', 'unknown')
                print(f"   ✅ PASSED - Stream created: {stream_id[:8]}...")
                tests_passed += 1
            else:
                print(f"   ⚠️  PARTIAL - Stream creation failed (expected): {data.get('error', 'Unknown')}")
                # This might fail due to invalid URL, which is expected
                tests_passed += 1
        else:
            print(f"   ❌ FAILED - HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
    
    # Test 5: Model Loading Test (via start_stream)
    total_tests += 1
    print("\n🧪 Test 5: Model Loading Test")
    try:
        # This will test if the model loading works (permission fix)
        response = requests.post(f"{service_url}/start_stream", 
                               headers=headers, 
                               json={"stream_url": "test://model-test"}, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if 'torch_load' in str(data) or 'Permission denied' in str(data):
                print(f"   ❌ FAILED - Model loading issues still present")
            else:
                print(f"   ✅ PASSED - No permission/compatibility errors")
                tests_passed += 1
        else:
            print(f"   ⚠️  PARTIAL - HTTP {response.status_code} (may be expected)")
            # Check if it's not a permission error
            if 'Permission denied' not in response.text and 'torch_load' not in response.text:
                tests_passed += 1
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
    
    # Summary
    print("\n" + "=" * 35)
    print(f"📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Serverless admin key is working correctly.")
        return True
    elif tests_passed >= total_tests * 0.8:
        print("⚠️  Most tests passed. Some issues may be expected in test environment.")
        return True
    else:
        print("❌ Multiple tests failed. Please check configuration.")
        return False

def get_service_info():
    """Get service URL and admin key from user or environment"""
    
    # Try to get from environment first
    service_url = os.environ.get('CCTV_SERVICE_URL')
    admin_key = os.environ.get('CCTV_ADMIN_KEY', 'cctv_admin_prod_2024')
    
    if not service_url:
        print("🌐 Serverless Service Configuration")
        print("=" * 35)
        print()
        
        # Ask user for service URL
        service_url = input("Enter your serverless service URL: ").strip()
        if not service_url:
            print("❌ Service URL is required")
            sys.exit(1)
        
        # Remove trailing slash
        service_url = service_url.rstrip('/')
        
        # Ask if user wants to use custom admin key
        use_custom = input(f"Use custom admin key? (current: {admin_key}) [y/N]: ").lower().strip()
        if use_custom == 'y':
            custom_key = input("Enter custom admin key: ").strip()
            if custom_key:
                admin_key = custom_key
    
    return service_url, admin_key

def main():
    print("🔧 CCTV Serverless Admin Key Test")
    print("=" * 35)
    print()
    
    # Get service configuration
    try:
        service_url, admin_key = get_service_info()
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        sys.exit(0)
    
    # Validate service URL
    if not service_url.startswith(('http://', 'https://')):
        print("❌ Invalid service URL. Must start with http:// or https://")
        sys.exit(1)
    
    print()
    print("🚀 Starting tests...")
    print()
    
    # Run tests
    success = test_serverless_admin(service_url, admin_key)
    
    print()
    print("💡 Tips:")
    print("   - Set CCTV_SERVICE_URL environment variable to skip URL prompt")
    print("   - Set CCTV_ADMIN_KEY environment variable for custom key")
    print("   - Use HTTPS URLs for production testing")
    print("   - Check service logs if tests fail")
    
    print()
    print("📋 Example environment setup:")
    print(f"   export CCTV_SERVICE_URL='{service_url}'")
    print(f"   export CCTV_ADMIN_KEY='{admin_key}'")
    print("   python3 test_serverless_admin.py")
    
    if success:
        print()
        print("✅ Serverless admin key testing completed successfully!")
        sys.exit(0)
    else:
        print()
        print("❌ Some tests failed. Please check your serverless configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
