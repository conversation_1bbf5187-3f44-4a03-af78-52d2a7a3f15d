<?php
/**
 * CCTV Monitoring API Client - PHP Example
 * ========================================
 * 
 * This example demonstrates how to interact with the CCTV Monitoring API using PHP.
 */

class CCTVAPIClient {
    private $baseUrl;
    private $apiKey;
    private $httpClient;
    
    /**
     * Initialize the API client
     * 
     * @param string $baseUrl Base URL of the CCTV API
     * @param string $apiKey Your API key
     */
    public function __construct($baseUrl, $apiKey) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
        
        // Initialize cURL
        $this->httpClient = curl_init();
        curl_setopt_array($this->httpClient, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'X-API-Key: ' . $apiKey,
                'Content-Type: application/json'
            ]
        ]);
    }
    
    /**
     * Make HTTP request to API
     * 
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array Response data
     * @throws Exception
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        curl_setopt($this->httpClient, CURLOPT_URL, $url);
        curl_setopt($this->httpClient, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        
        if ($data !== null) {
            curl_setopt($this->httpClient, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            curl_setopt($this->httpClient, CURLOPT_POSTFIELDS, null);
        }
        
        $response = curl_exec($this->httpClient);
        $httpCode = curl_getinfo($this->httpClient, CURLINFO_HTTP_CODE);
        
        if ($response === false) {
            throw new Exception('cURL error: ' . curl_error($this->httpClient));
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = isset($decodedResponse['message']) ? $decodedResponse['message'] : 'HTTP Error ' . $httpCode;
            throw new Exception($errorMessage);
        }
        
        return $decodedResponse;
    }
    
    // Stream Management Methods
    
    /**
     * List all active streams
     * 
     * @return array Array of stream objects
     */
    public function listStreams() {
        $response = $this->makeRequest('GET', '/streams');
        return $response['data']['streams'];
    }
    
    /**
     * Create and start a new stream
     * 
     * @param string $streamUrl Stream URL
     * @param string $name Optional stream name
     * @return array Created stream object
     */
    public function createStream($streamUrl, $name = null) {
        $data = ['stream_url' => $streamUrl];
        if ($name !== null) {
            $data['name'] = $name;
        }
        
        $response = $this->makeRequest('POST', '/streams', $data);
        return $response['data'];
    }
    
    /**
     * Get specific stream information
     * 
     * @param string $streamId Stream ID
     * @return array Stream object
     */
    public function getStream($streamId) {
        $response = $this->makeRequest('GET', "/streams/{$streamId}");
        return $response['data'];
    }
    
    /**
     * Stop and delete a stream
     * 
     * @param string $streamId Stream ID
     * @return bool Success status
     */
    public function deleteStream($streamId) {
        $response = $this->makeRequest('DELETE', "/streams/{$streamId}");
        return $response['success'];
    }
    
    // Detection Methods
    
    /**
     * Get current detections for a stream
     * 
     * @param string $streamId Stream ID
     * @return array Array of detection objects
     */
    public function getDetections($streamId) {
        $response = $this->makeRequest('GET', "/streams/{$streamId}/detections");
        return $response['data']['detections'];
    }
    
    /**
     * Get detailed statistics for a stream
     * 
     * @param string $streamId Stream ID
     * @return array Statistics object
     */
    public function getStatistics($streamId) {
        $response = $this->makeRequest('GET', "/streams/{$streamId}/statistics");
        return $response['data'];
    }
    
    /**
     * Reset statistics for a stream
     * 
     * @param string $streamId Stream ID
     * @return bool Success status
     */
    public function resetStatistics($streamId) {
        $response = $this->makeRequest('DELETE', "/streams/{$streamId}/statistics");
        return $response['success'];
    }
    
    // System Methods
    
    /**
     * Get overall system status
     * 
     * @return array System status object
     */
    public function getSystemStatus() {
        $response = $this->makeRequest('GET', '/system/status');
        return $response['data'];
    }
    
    /**
     * Simple health check
     * 
     * @return array Health status
     */
    public function healthCheck() {
        $response = $this->makeRequest('GET', '/system/health');
        return $response;
    }
    
    // Webhook Methods
    
    /**
     * List registered webhooks
     * 
     * @return array Array of webhook objects
     */
    public function listWebhooks() {
        $response = $this->makeRequest('GET', '/webhooks');
        return $response['data']['webhooks'];
    }
    
    /**
     * Register a webhook endpoint
     * 
     * @param string $url Webhook URL
     * @param array $events Array of event types
     * @return array Webhook object
     */
    public function registerWebhook($url, $events) {
        $data = ['url' => $url, 'events' => $events];
        $response = $this->makeRequest('POST', '/webhooks', $data);
        return $response['data'];
    }
    
    /**
     * Delete a webhook
     * 
     * @param string $webhookId Webhook ID
     * @return bool Success status
     */
    public function deleteWebhook($webhookId) {
        $response = $this->makeRequest('DELETE', "/webhooks/{$webhookId}");
        return $response['success'];
    }
    
    /**
     * Close cURL handle
     */
    public function __destruct() {
        if ($this->httpClient) {
            curl_close($this->httpClient);
        }
    }
}

// Example Usage
function main() {
    $client = new CCTVAPIClient(
        'http://localhost:5000/api/v1',
        'your-api-key-here'
    );
    
    try {
        // Health check
        echo "🔍 Checking system health...\n";
        $health = $client->healthCheck();
        echo "✅ System status: {$health['status']}\n";
        
        // Get system status
        echo "\n📊 Getting system status...\n";
        $status = $client->getSystemStatus();
        echo "📈 Active streams: {$status['streams']['active']}\n";
        echo "🚗 Total detections: {$status['detections']['total_detections']}\n";
        
        // Create a new stream
        echo "\n🎥 Creating new stream...\n";
        $streamUrl = "http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8";
        $stream = $client->createStream($streamUrl, "Test Stream");
        $streamId = $stream['stream_id'];
        echo "✅ Stream created: {$streamId}\n";
        
        // Wait for stream to start processing
        echo "\n⏳ Waiting for stream to process...\n";
        sleep(10);
        
        // Get detections
        echo "\n🔍 Getting current detections...\n";
        $detections = $client->getDetections($streamId);
        echo "🚗 Current detections: " . count($detections) . "\n";
        
        foreach (array_slice($detections, 0, 3) as $detection) {
            $confidence = number_format($detection['confidence'], 2);
            echo "  - {$detection['vehicle_type']}: confidence {$confidence}\n";
        }
        
        // Get statistics
        echo "\n📊 Getting stream statistics...\n";
        $stats = $client->getStatistics($streamId);
        echo "📈 Frame count: {$stats['frame_count']}\n";
        echo "🚗 Unique vehicles: {$stats['tracking_stats']['total_unique']}\n";
        
        // Register webhook (optional)
        echo "\n🔗 Registering webhook...\n";
        try {
            $webhook = $client->registerWebhook(
                "https://your-server.com/webhook",
                ["vehicle_detected", "unique_vehicle_counted"]
            );
            echo "✅ Webhook registered: {$webhook['webhook_id']}\n";
        } catch (Exception $e) {
            echo "⚠️ Webhook registration failed: {$e->getMessage()}\n";
        }
        
        // List all streams
        echo "\n📋 Listing all streams...\n";
        $streams = $client->listStreams();
        foreach ($streams as $stream) {
            $url = substr($stream['stream_url'], 0, 50) . '...';
            echo "  - {$stream['stream_id']}: {$url}\n";
        }
        
        // Clean up - delete the stream
        echo "\n🗑️ Cleaning up - deleting stream {$streamId}...\n";
        $client->deleteStream($streamId);
        echo "✅ Stream deleted\n";
        
    } catch (Exception $e) {
        echo "❌ Error: {$e->getMessage()}\n";
    }
}

// Utility Functions

/**
 * Monitor a stream continuously for a specified duration
 * 
 * @param CCTVAPIClient $client API client instance
 * @param string $streamId Stream ID to monitor
 * @param int $duration Duration in seconds
 */
function monitorStreamContinuously($client, $streamId, $duration = 60) {
    echo "🔍 Monitoring stream {$streamId} for {$duration} seconds...\n";
    
    $startTime = time();
    $lastDetectionCount = 0;
    
    while (time() - $startTime < $duration) {
        try {
            $detections = $client->getDetections($streamId);
            $currentCount = count($detections);
            
            if ($currentCount !== $lastDetectionCount) {
                echo "🚗 Detection count changed: {$currentCount}\n";
                $lastDetectionCount = $currentCount;
            }
            
            sleep(5); // Wait 5 seconds
            
        } catch (Exception $e) {
            echo "❌ Monitoring error: {$e->getMessage()}\n";
            break;
        }
    }
    
    echo "✅ Monitoring completed\n";
}

/**
 * Create multiple streams in batch
 * 
 * @param CCTVAPIClient $client API client instance
 * @param array $streamUrls Array of stream URLs
 * @return array Array of created stream IDs
 */
function batchCreateStreams($client, $streamUrls) {
    $streamIds = [];
    
    foreach ($streamUrls as $i => $url) {
        try {
            $stream = $client->createStream($url, "Batch Stream " . ($i + 1));
            $streamIds[] = $stream['stream_id'];
            echo "✅ Created stream " . ($i + 1) . ": {$stream['stream_id']}\n";
        } catch (Exception $e) {
            echo "❌ Failed to create stream " . ($i + 1) . ": {$e->getMessage()}\n";
        }
    }
    
    return $streamIds;
}

/**
 * Export statistics to JSON file
 * 
 * @param CCTVAPIClient $client API client instance
 * @param string $streamId Stream ID
 * @param string $filename Output filename
 */
function exportStatisticsToJSON($client, $streamId, $filename) {
    try {
        $stats = $client->getStatistics($streamId);
        
        $exportData = [
            'timestamp' => date('c'),
            'stream_id' => $streamId,
            'frame_count' => $stats['frame_count'],
            'total_detections' => $stats['detection_stats']['total_detections'],
            'unique_vehicles' => $stats['tracking_stats']['total_unique'],
            'vehicle_breakdown' => $stats['tracking_stats']['by_type']
        ];
        
        file_put_contents($filename, json_encode($exportData, JSON_PRETTY_PRINT));
        echo "✅ Statistics exported to {$filename}\n";
        
    } catch (Exception $e) {
        echo "❌ Export failed: {$e->getMessage()}\n";
    }
}

/**
 * Simple webhook receiver
 * 
 * @param int $port Port to listen on
 */
function startWebhookReceiver($port = 8080) {
    echo "🔗 Starting webhook receiver on port {$port}...\n";
    echo "📡 Webhook URL: http://localhost:{$port}/webhook.php\n";
    echo "💡 Create webhook.php file to handle incoming webhooks\n";
    
    // Example webhook.php content:
    $webhookContent = '<?php
// Webhook receiver for CCTV API
header("Content-Type: application/json");

$input = file_get_contents("php://input");
$data = json_decode($input, true);

if ($data) {
    $eventType = $data["event_type"];
    $timestamp = $data["timestamp"];
    $eventData = $data["data"];
    
    error_log("🔔 Webhook received: {$eventType} at {$timestamp}");
    
    switch ($eventType) {
        case "vehicle_detected":
            error_log("🚗 Vehicle detected: {$eventData[\"vehicle_type\"]} (confidence: {$eventData[\"confidence\"]})");
            break;
        case "unique_vehicle_counted":
            error_log("📊 Unique vehicle counted: {$eventData[\"vehicle_type\"]} (total: {$eventData[\"total_unique_count\"]})");
            break;
        case "stream_started":
            error_log("🎥 Stream started: {$eventData[\"stream_id\"]}");
            break;
        case "stream_stopped":
            error_log("⏹️ Stream stopped: {$eventData[\"stream_id\"]}");
            break;
    }
    
    echo json_encode(["received" => true]);
} else {
    http_response_code(400);
    echo json_encode(["error" => "Invalid JSON"]);
}
?>';
    
    file_put_contents('webhook.php', $webhookContent);
    echo "✅ Created webhook.php file\n";
}

// Run example if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    main();
}
?>
