#!/usr/bin/env python3
import os

# Set torch cache directory to writable location before importing torch
os.environ.setdefault('TORCH_HOME', '/app/.cache/torch')
os.environ.setdefault('XDG_CACHE_HOME', '/app/.cache')

from app import app

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8080))
    host = os.environ.get("HOST", "0.0.0.0")
    
    print(f"Starting server on {host}:{port}")
    print(f"SERVERLESS mode: {os.environ.get('SERVERLESS', 'false')}")
    
    # For serverless environments
    if os.environ.get("SERVERLESS", "false").lower() == "true":
        # Use gunicorn for better serverless performance
        import subprocess
        import sys
        cmd = [
            "gunicorn",
            "--bind", f"{host}:{port}",
            "--workers", "1",
            "--worker-class", "gevent",
            "--worker-connections", "1000",
            "--timeout", "120",
            "--keep-alive", "2",
            "--max-requests", "1000",
            "--max-requests-jitter", "100",
            "app:app"
        ]
        print(f"Running command: {' '.join(cmd)}")
        subprocess.run(cmd)
    else:
        # Regular Flask development server
        app.run(debug=False, host=host, port=port, threaded=True)
