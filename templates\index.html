{% extends "base.html" %}

{% block title %}CCTV Monitoring - Home{% endblock %}

{% block content %}
<div class="row">
    <!-- Control Panel -->
    <div class="col-lg-4 col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-cog"></i> Control Panel</h5>
            </div>
            <div class="card-body">
                <!-- Stream URL Input -->
                <div class="mb-3">
                    <label for="streamUrl" class="form-label">Stream URL</label>
                    <div class="input-group">
                        <input type="url" class="form-control" id="streamUrl"
                            placeholder="http://example.com/stream.m3u8"
                            value="http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8">
                        <button class="btn btn-outline-secondary" type="button" id="validateBtn">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                    <div class="form-text">Masukkan URL stream CCTV (HTTP, RTSP, atau M3U8)</div>
                </div>

                <!-- Stream Controls -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="startStreamBtn">
                        <i class="fas fa-play"></i> Start Stream
                    </button>
                    <button type="button" class="btn btn-danger" id="stopStreamBtn" disabled>
                        <i class="fas fa-stop"></i> Stop Stream
                    </button>
                </div>

                <!-- Stream Status -->
                <div class="mt-3">
                    <div class="alert alert-info" id="streamStatus">
                        <i class="fas fa-info-circle"></i> Ready to start monitoring
                    </div>
                </div>

                <!-- Detection Settings -->
                <div class="mt-4">
                    <h6><i class="fas fa-sliders-h"></i> Detection Settings</h6>
                    <div class="mb-2">
                        <label for="confidenceSlider" class="form-label">Confidence Threshold: <span
                                id="confidenceValue">0.25</span></label>
                        <input type="range" class="form-range" id="confidenceSlider" min="0.05" max="0.9" step="0.05"
                            value="0.25">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="strictFiltering" checked>
                        <label class="form-check-label" for="strictFiltering">
                            Strict False Positive Filtering
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="motorcycleMode">
                        <label class="form-check-label" for="motorcycleMode">
                            Motorcycle Sensitive Mode
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Panel -->
        <div class="card shadow mt-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Detection Statistics</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="resetStatistics()"
                        title="Reset Manual">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="showAutoResetModal()"
                        title="Auto-Reset Settings">
                        <i class="fas fa-clock"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="stat-item">
                            <h3 class="text-primary" id="totalDetections">0</h3>
                            <small>Total Detections</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <h3 class="text-success" id="uniqueVehicles">0</h3>
                            <small>Unique Vehicles</small>
                        </div>
                    </div>
                </div>

                <hr>

                <div id="vehicleBreakdown">
                    <h6>Vehicle Types:</h6>
                    <div class="vehicle-stats">
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-car text-success"></i> Mobil:</span>
                            <span id="carCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-motorcycle text-danger"></i> Motor:</span>
                            <span id="motorcycleCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-bus text-primary"></i> Bus:</span>
                            <span id="busCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-truck text-warning"></i> Truk:</span>
                            <span id="truckCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-bicycle text-info"></i> Sepeda:</span>
                            <span id="bicycleCount">0</span>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <div id="auto-reset-info" class="alert alert-info py-2" style="display: none;">
                        <small>
                            <i class="fas fa-clock"></i> Auto-reset: <span id="reset-time">00:00</span>
                            <span id="reset-status" class="badge badge-success ml-2">Aktif</span>
                            <br>
                            <span class="text-muted">Next reset: <span id="next-reset">-</span></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Display -->
    <div class="col-lg-8 col-md-12">
        <div class="card shadow">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-video"></i> Live Stream</h5>
            </div>
            <div class="card-body p-0">
                <div class="video-container">
                    <div id="videoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <i class="fas fa-video fa-5x text-muted mb-3"></i>
                            <h4 class="text-muted">No Active Stream</h4>
                            <p class="text-muted">Enter a stream URL and click "Start Stream" to begin monitoring</p>
                        </div>
                    </div>
                    <img id="videoStream" class="video-stream" style="display: none;" alt="Live Stream">
                </div>

                <!-- Video Controls -->
                <div class="video-controls p-3 bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> Frame: <span id="frameCount">0</span> |
                                <i class="fas fa-signal"></i> Status: <span id="connectionStatus">Disconnected</span>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="fullscreenBtn">
                                <i class="fas fa-expand"></i> Fullscreen
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="screenshotBtn">
                                <i class="fas fa-camera"></i> Screenshot
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detection Log -->
        <div class="card shadow mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-list"></i> Detection Log</h5>
            </div>
            <div class="card-body">
                <div class="detection-log" id="detectionLog">
                    <p class="text-muted">Detection events will appear here...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Connecting to stream...</p>
            </div>
        </div>
    </div>
</div>

<!-- Auto-Reset Settings Modal -->
<div class="modal fade" id="autoResetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-clock"></i> Auto-Reset Settings</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="autoResetForm">
                    <div class="mb-3">
                        <label class="form-label">Auto-Reset Status</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoResetEnabled" checked>
                            <label class="form-check-label" for="autoResetEnabled">
                                Enable automatic daily reset
                            </label>
                        </div>
                        <small class="form-text text-muted">When enabled, statistics will automatically reset every day
                            at the specified time.</small>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <label for="resetHour" class="form-label">Hour (0-23)</label>
                            <input type="number" class="form-control" id="resetHour" min="0" max="23" value="0">
                        </div>
                        <div class="col-6">
                            <label for="resetMinute" class="form-label">Minute (0-59)</label>
                            <input type="number" class="form-control" id="resetMinute" min="0" max="59" value="0">
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                <strong>Current Status:</strong><br>
                                Auto-reset: <span id="currentResetStatus">Loading...</span><br>
                                Reset time: <span id="currentResetTime">Loading...</span><br>
                                Next reset: <span id="currentNextReset">Loading...</span>
                            </small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="performManualReset()">
                    <i class="fas fa-redo"></i> Reset Now
                </button>
                <button type="button" class="btn btn-primary" onclick="saveAutoResetSettings()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> Error</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="errorMessage">An error occurred.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize page functionality
    $(document).ready(function () {
        initializeStreamMonitoring();
    });
</script>
{% endblock %}