#!/usr/bin/env python3
"""
CCTV Video Streaming Client - Python Example
===========================================

This example demonstrates how to consume live video streams with detection overlay
from the CCTV Monitoring API using Python.
"""

import requests
import cv2
import numpy as np
import base64
import time
import threading
from io import BytesIO
from PIL import Image
import json

class CCTVVideoStreamClient:
    """Python client for consuming CCTV video streams via API"""
    
    def __init__(self, base_url: str, api_key: str = None):
        """
        Initialize the video streaming client
        
        Args:
            base_url: Base URL of the CCTV API
            api_key: API key (optional for public shared streams)
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({'X-API-Key': api_key})
    
    def get_stream_info(self, stream_id: str, shared: bool = False) -> dict:
        """Get stream information and capabilities"""
        endpoint = f"/shared/streams/{stream_id}/info" if shared else f"/streams/{stream_id}/stream_info"
        
        try:
            response = self.session.get(f"{self.base_url}{endpoint}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error getting stream info: {e}")
            return None
    
    def get_current_frame(self, stream_id: str, shared: bool = False) -> np.ndarray:
        """Get current frame as OpenCV image"""
        endpoint = f"/shared/streams/{stream_id}/frame" if shared else f"/streams/{stream_id}/frame"
        
        try:
            response = self.session.get(f"{self.base_url}{endpoint}")
            response.raise_for_status()
            
            data = response.json()
            if data['success']:
                # Decode base64 image
                frame_data = base64.b64decode(data['data']['frame_base64'])
                
                # Convert to OpenCV format
                image = Image.open(BytesIO(frame_data))
                frame = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
                
                return frame
            else:
                print(f"Error: {data['message']}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Error getting frame: {e}")
            return None
    
    def stream_video_opencv(self, stream_id: str, shared: bool = False, window_name: str = "CCTV Stream"):
        """
        Stream video using OpenCV display
        
        Args:
            stream_id: Stream ID to display
            shared: Whether to use shared stream endpoint
            window_name: OpenCV window name
        """
        endpoint = f"/shared/streams/{stream_id}/video_feed" if shared else f"/streams/{stream_id}/video_feed"
        url = f"{self.base_url}{endpoint}"
        
        print(f"🎥 Starting video stream: {url}")
        print("Press 'q' to quit, 's' to save screenshot")
        
        try:
            # Open video stream
            cap = cv2.VideoCapture(url)
            
            if not cap.isOpened():
                print("❌ Failed to open video stream")
                return
            
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    print("⚠️ Failed to read frame, reconnecting...")
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(url)
                    continue
                
                frame_count += 1
                
                # Add frame counter
                cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # Display frame
                cv2.imshow(window_name, frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    # Save screenshot
                    filename = f"screenshot_{stream_id}_{int(time.time())}.jpg"
                    cv2.imwrite(filename, frame)
                    print(f"📸 Screenshot saved: {filename}")
                
            cap.release()
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"❌ Error in video streaming: {e}")
    
    def stream_video_requests(self, stream_id: str, shared: bool = False, save_frames: bool = False):
        """
        Stream video using requests library (for processing frames)
        
        Args:
            stream_id: Stream ID to process
            shared: Whether to use shared stream endpoint
            save_frames: Whether to save frames to disk
        """
        endpoint = f"/shared/streams/{stream_id}/video_feed" if shared else f"/streams/{stream_id}/video_feed"
        url = f"{self.base_url}{endpoint}"
        
        print(f"🎥 Processing video stream: {url}")
        
        try:
            headers = {}
            if self.api_key:
                headers['X-API-Key'] = self.api_key
            
            response = self.session.get(url, headers=headers, stream=True)
            response.raise_for_status()
            
            frame_count = 0
            buffer = b""
            
            for chunk in response.iter_content(chunk_size=1024):
                buffer += chunk
                
                # Look for frame boundaries
                while b'\xff\xd8' in buffer and b'\xff\xd9' in buffer:
                    # Find JPEG start and end
                    start = buffer.find(b'\xff\xd8')
                    end = buffer.find(b'\xff\xd9', start) + 2
                    
                    if end > start:
                        # Extract frame
                        frame_data = buffer[start:end]
                        buffer = buffer[end:]
                        
                        # Process frame
                        self._process_frame(frame_data, frame_count, save_frames, stream_id)
                        frame_count += 1
                        
                        if frame_count % 30 == 0:  # Print every 30 frames
                            print(f"📊 Processed {frame_count} frames")
                    else:
                        break
                        
        except requests.exceptions.RequestException as e:
            print(f"❌ Error in video streaming: {e}")
        except KeyboardInterrupt:
            print("\n⏹️ Stream stopped by user")
    
    def _process_frame(self, frame_data: bytes, frame_count: int, save_frames: bool, stream_id: str):
        """Process individual frame"""
        try:
            # Convert to OpenCV format
            nparr = np.frombuffer(frame_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is not None:
                # Add your frame processing logic here
                # For example: object detection, analysis, etc.
                
                if save_frames and frame_count % 60 == 0:  # Save every 60th frame
                    filename = f"frame_{stream_id}_{frame_count}.jpg"
                    cv2.imwrite(filename, frame)
                    print(f"💾 Saved frame: {filename}")
                    
        except Exception as e:
            print(f"❌ Error processing frame {frame_count}: {e}")
    
    def monitor_multiple_streams(self, stream_configs: list):
        """
        Monitor multiple streams simultaneously
        
        Args:
            stream_configs: List of dicts with 'stream_id' and 'shared' keys
        """
        threads = []
        
        for i, config in enumerate(stream_configs):
            stream_id = config['stream_id']
            shared = config.get('shared', False)
            window_name = f"Stream {i+1}: {stream_id[:8]}"
            
            thread = threading.Thread(
                target=self.stream_video_opencv,
                args=(stream_id, shared, window_name)
            )
            thread.daemon = True
            thread.start()
            threads.append(thread)
        
        # Wait for all threads
        try:
            for thread in threads:
                thread.join()
        except KeyboardInterrupt:
            print("\n⏹️ All streams stopped")

def main():
    """Example usage"""
    
    # Initialize client
    client = CCTVVideoStreamClient(
        base_url='http://localhost:5000/api/v1',
        api_key='cctv_admin_Q-QTkUndp7CTecdIAs9NCwcpUKsYMPHZ0xUEhsgGuMk'  # Replace with your API key
    )
    
    # Example 1: Get stream info
    print("📋 Getting stream info...")
    # Replace with actual stream ID
    stream_id = "your-stream-id-here"
    
    info = client.get_stream_info(stream_id)
    if info and info['success']:
        print(f"✅ Stream info: {info['data']['capabilities']}")
        print(f"📊 Current stats: {info['data']['current_stats']}")
    
    # Example 2: Get single frame
    print("\n📸 Getting current frame...")
    frame = client.get_current_frame(stream_id)
    if frame is not None:
        print(f"✅ Frame captured: {frame.shape}")
        cv2.imshow("Current Frame", frame)
        cv2.waitKey(3000)  # Show for 3 seconds
        cv2.destroyAllWindows()
    
    # Example 3: Stream video with OpenCV
    print("\n🎥 Starting live video stream...")
    print("Note: Replace 'your-stream-id-here' with actual stream ID")
    # client.stream_video_opencv(stream_id)
    
    # Example 4: Monitor multiple streams
    # stream_configs = [
    #     {'stream_id': 'stream-1', 'shared': False},
    #     {'stream_id': 'stream-2', 'shared': True}
    # ]
    # client.monitor_multiple_streams(stream_configs)

def demo_shared_stream():
    """Demo for accessing shared streams without API key"""
    
    # Client without API key for public shared streams
    client = CCTVVideoStreamClient('http://localhost:5000/api/v1')
    
    # List public shared streams
    try:
        response = requests.get('http://localhost:5000/api/v1/shared/streams')
        if response.status_code == 200:
            data = response.json()
            public_streams = data['data']['public_streams']
            
            print(f"📋 Found {len(public_streams)} public shared streams:")
            for stream in public_streams:
                print(f"  - {stream['stream_id']}")
                
                # Stream the first public stream
                if public_streams:
                    first_stream = public_streams[0]['stream_id']
                    print(f"\n🎥 Streaming public stream: {first_stream}")
                    client.stream_video_opencv(first_stream, shared=True)
        else:
            print("❌ Failed to get shared streams list")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🎥 CCTV Video Streaming Client")
    print("=" * 40)
    
    # Run main demo
    main()
    
    # Uncomment to test shared streams
    # demo_shared_stream()
