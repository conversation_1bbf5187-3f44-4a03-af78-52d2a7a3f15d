"""
CCTV Monitoring System - REST API
=================================

Comprehensive REST API for external monitoring and integration.
Allows other projects to monitor CCTV streams programmatically.
"""

from flask import Blueprint, request, jsonify, Response, stream_template
from functools import wraps
import json
import time
import uuid
import threading
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional
import hashlib
import secrets
import cv2
import base64
import numpy as np


# Import will be done dynamically to avoid circular imports

def get_app_module():
    """Get app module dynamically to avoid circular imports"""
    import app
    return app

# Create API blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# Configure logging
logger = logging.getLogger(__name__)

# Optional: Database integration (MySQL)
try:
    from db import init_db, save_stream, update_stream_status, get_stream as db_get_stream
    _db_available = True
    # Initialize DB tables on import (safe to call multiple times)
    try:
        init_db()
    except Exception as e:
        logger.warning(f"DB init failed: {e}")
except Exception as e:  # db module not present or import error
    _db_available = False
    logger.warning(f"DB module unavailable; proceeding without persistence: {e}")

# API Configuration
API_KEYS = {}  # Will be loaded from config or database
WEBHOOK_ENDPOINTS = {}  # Webhook URLs for notifications
RATE_LIMITS = {}  # Rate limiting per API key
STREAM_ACCESS = {}  # Stream access permissions per API key
SHARED_STREAMS = {}  # Publicly shared streams

class APIKeyManager:
    """Manage API keys and authentication"""

    def __init__(self):
        self.api_keys = {}
        self.load_api_keys()

    def load_api_keys(self):
        """Load API keys from configuration"""
        # Load static admin key from configuration
        from admin_config import AdminConfig, ADMIN_PERMISSIONS
        admin_key = AdminConfig.get_admin_key()

        self.api_keys[admin_key] = {
            'name': 'Admin Key',
            'permissions': ADMIN_PERMISSIONS,
            'created_at': datetime.now(),
            'last_used': None,
            'usage_count': 0,
            'is_admin': True,
            'is_static': True
        }
        logger.info(f"Loaded static admin API key: {admin_key}")

    def generate_api_key(self, name: str, permissions: List[str]) -> str:
        """Generate new API key"""
        api_key = f"cctv_{secrets.token_urlsafe(32)}"
        self.api_keys[api_key] = {
            'name': name,
            'permissions': permissions,
            'created_at': datetime.now(),
            'last_used': None,
            'usage_count': 0
        }
        return api_key

    def validate_api_key(self, api_key: str, required_permission: str = 'read') -> bool:
        """Validate API key and check permissions"""
        if api_key not in self.api_keys:
            return False

        key_info = self.api_keys[api_key]
        if required_permission not in key_info['permissions']:
            return False

        # Update usage statistics
        key_info['last_used'] = datetime.now()
        key_info['usage_count'] += 1

        return True

    def get_api_key_info(self, api_key: str) -> Optional[Dict]:
        """Get API key information"""
        return self.api_keys.get(api_key)

# Initialize API key manager
api_key_manager = APIKeyManager()

def require_api_key(permission='read'):
    """Decorator to require API key authentication"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')

            if not api_key:
                return jsonify({
                    'error': 'API key required',
                    'message': 'Please provide API key in X-API-Key header or api_key parameter'
                }), 401

            if not api_key_manager.validate_api_key(api_key, permission):
                return jsonify({
                    'error': 'Invalid API key or insufficient permissions',
                    'message': f'Valid API key with {permission} permission required'
                }), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def rate_limit(max_requests=100, window_minutes=60):
    """Rate limiting decorator"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')

            if api_key:
                now = datetime.now()
                window_start = now - timedelta(minutes=window_minutes)

                if api_key not in RATE_LIMITS:
                    RATE_LIMITS[api_key] = []

                # Clean old requests
                RATE_LIMITS[api_key] = [
                    req_time for req_time in RATE_LIMITS[api_key]
                    if req_time > window_start
                ]

                # Check rate limit
                if len(RATE_LIMITS[api_key]) >= max_requests:
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'message': f'Maximum {max_requests} requests per {window_minutes} minutes'
                    }), 429

                # Add current request
                RATE_LIMITS[api_key].append(now)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# ============================================================================
# STREAM MANAGEMENT API ENDPOINTS
# ============================================================================

@api_bp.route('/streams', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=200)
def list_streams():
    """List all active streams"""
    try:
        app = get_app_module()

        with app.stream_lock:
            streams = []
            for stream_id, stream in app.active_streams.items():
                streams.append({
                    'stream_id': stream_id,
                    'stream_url': stream.stream_url,
                    'is_active': stream.is_active,
                    'frame_count': stream.frame_count,
                    'detection_stats': stream.detection_stats
                })

        return jsonify({
            'success': True,
            'data': {
                'streams': streams,
                'total_count': len(streams)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error listing streams: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams', methods=['POST'])
@require_api_key('write')
@rate_limit(max_requests=50)
def create_stream():
    """Create and start a new stream"""
    try:
        data = request.get_json()

        if not data or 'stream_url' not in data:
            return jsonify({
                'success': False,
                'error': 'Missing required field',
                'message': 'stream_url is required'
            }), 400

        stream_url = data['stream_url'].strip()
        stream_name = data.get('name', f'Stream_{int(time.time())}')

        # Validate stream URL
        app = get_app_module()
        is_valid, message = app.validate_stream_url(stream_url)

        if not is_valid:
            return jsonify({
                'success': False,
                'error': 'Invalid stream URL',
                'message': message
            }), 400

        # Generate unique stream ID
        stream_id = str(uuid.uuid4())

        app = get_app_module()

        # Create new stream instance
        cctv_stream = app.CCTVStream(stream_url, stream_id)

        # Connect to stream
        if cctv_stream.connect_stream():
            with app.stream_lock:
                app.active_streams[stream_id] = cctv_stream

            # Persist stream info to DB (best-effort)
            try:
                if 'save_stream' in globals():
                    save_stream(stream_id, stream_url, stream_name, 'active')
            except Exception as e:
                logger.warning(f"DB persist failed for stream {stream_id}: {e}")

            return jsonify({
                'success': True,
                'data': {
                    'stream_id': stream_id,
                    'stream_url': stream_url,
                    'name': stream_name,
                    'status': 'active'
                },
                'message': 'Stream created and started successfully'
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to connect to stream',
                'message': 'Could not establish connection to the provided stream URL'
            }), 400

    except Exception as e:
        logger.error(f"Error creating stream: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=300)
def get_stream(stream_id):
    """Get specific stream information"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

            # Get active vehicles
            active_vehicles = stream.vehicle_tracker.get_active_vehicles()

            return jsonify({
                'success': True,
                'data': {
                    'stream_id': stream_id,
                    'stream_url': stream.stream_url,
                    'is_active': stream.is_active,
                    'frame_count': stream.frame_count,
                    'detection_stats': stream.detection_stats,
                    'active_vehicles': len(active_vehicles),
                    'tracking_stats': stream.vehicle_tracker.get_statistics()
                },
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"Error getting stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>', methods=['DELETE'])
@require_api_key('write')
@rate_limit(max_requests=50)
def delete_stream(stream_id):
    """Stop and delete a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]
            stream.disconnect_stream()
            del app.active_streams[stream_id]

        # Update DB status best-effort
        try:
            if 'update_stream_status' in globals():
                update_stream_status(stream_id, 'deleted')
        except Exception as e:
            logger.warning(f"DB status update failed for {stream_id}: {e}")

        return jsonify({
            'success': True,
            'message': f'Stream {stream_id} stopped and deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# DETECTION AND STATISTICS API ENDPOINTS
# ============================================================================

@api_bp.route('/streams/<stream_id>/detections', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=500)
def get_detections(stream_id):
    """Get current detections for a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found'
                }), 404

            stream = app.active_streams[stream_id]
            active_vehicles = stream.vehicle_tracker.get_active_vehicles()

            detections = []
            for vehicle_id, vehicle_info in active_vehicles:
                x1, y1, x2, y2 = vehicle_info['bbox']
                detections.append({
                    'vehicle_id': vehicle_id,
                    'class_id': vehicle_info['class_id'],
                    'confidence': float(vehicle_info['confidence']),
                    'bbox': {
                        'x1': float(x1), 'y1': float(y1),
                        'x2': float(x2), 'y2': float(y2)
                    },
                    'center': {
                        'x': float(vehicle_info['center'][0]),
                        'y': float(vehicle_info['center'][1])
                    },
                    'frames_seen': vehicle_info['frames_seen'],
                    'counted_as_unique': vehicle_info['counted_as_unique']
                })

            return jsonify({
                'success': True,
                'data': {
                    'stream_id': stream_id,
                    'detections': detections,
                    'detection_count': len(detections),
                    'frame_count': stream.frame_count
                },
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"Error getting detections for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/statistics', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=300)
def get_statistics(stream_id):
    """Get detailed statistics for a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found'
                }), 404

            stream = app.active_streams[stream_id]
            tracking_stats = stream.vehicle_tracker.get_statistics()

            return jsonify({
                'success': True,
                'data': {
                    'stream_id': stream_id,
                    'frame_count': stream.frame_count,
                    'detection_stats': stream.detection_stats,
                    'tracking_stats': tracking_stats,
                    'is_active': stream.is_active
                },
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"Error getting statistics for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/statistics', methods=['DELETE'])
@require_api_key('write')
@rate_limit(max_requests=20)
def reset_statistics(stream_id):
    """Reset statistics for a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found'
                }), 404

            stream = app.active_streams[stream_id]
            stream.vehicle_tracker.reset_counts()
            stream.detection_stats = {
                'total_detections': 0,
                'unique_vehicles': 0,
                'by_type': {}
            }

        return jsonify({
            'success': True,
            'message': f'Statistics reset for stream {stream_id}'
        })

    except Exception as e:
        logger.error(f"Error resetting statistics for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/auto_reset', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=100)
def get_auto_reset_info(stream_id):
    """Get auto-reset information for a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        reset_info = stream.vehicle_tracker.get_reset_info()

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'auto_reset_info': reset_info
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting auto-reset info for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/auto_reset', methods=['POST'])
@require_api_key('write')
@rate_limit(max_requests=20)
def configure_auto_reset(stream_id):
    """Configure auto-reset settings for a stream"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        data = request.get_json() or {}

        # Configure reset time
        if 'reset_hour' in data or 'reset_minute' in data:
            hour = data.get('reset_hour', stream.vehicle_tracker.reset_hour)
            minute = data.get('reset_minute', stream.vehicle_tracker.reset_minute)

            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                return jsonify({
                    'success': False,
                    'error': 'Invalid time',
                    'message': 'Hour must be 0-23, minute must be 0-59'
                }), 400

            stream.vehicle_tracker.set_reset_time(hour, minute)

        # Enable/disable auto-reset
        if 'enabled' in data:
            enabled = bool(data['enabled'])
            stream.vehicle_tracker.enable_auto_reset(enabled)

        # Get updated info
        reset_info = stream.vehicle_tracker.get_reset_info()

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'auto_reset_info': reset_info
            },
            'message': 'Auto-reset configuration updated successfully'
        })

    except Exception as e:
        logger.error(f"Error configuring auto-reset for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/manual_reset', methods=['POST'])
@require_api_key('write')
@rate_limit(max_requests=10)
def manual_reset_stream(stream_id):
    """Manually reset stream statistics"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        # Perform manual reset
        stream.vehicle_tracker.manual_reset()

        # Send webhook notification
        try:
            send_webhook_notification('manual_reset', {
                'stream_id': stream_id,
                'reset_time': datetime.now().isoformat(),
                'reset_type': 'manual'
            })
        except Exception as e:
            logger.warning(f"Failed to send manual reset webhook: {e}")

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'reset_time': datetime.now().isoformat(),
                'reset_type': 'manual'
            },
            'message': 'Stream statistics manually reset successfully'
        })

    except Exception as e:
        logger.error(f"Error manually resetting stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# GLOBAL AUTO-RESET API ENDPOINTS
# ============================================================================

@api_bp.route('/system/auto_reset', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=50)
def get_global_auto_reset_info():
    """Get auto-reset information for all streams"""
    try:
        app = get_app_module()

        streams_info = []
        with app.stream_lock:
            for stream_id, stream in app.active_streams.items():
                reset_info = stream.vehicle_tracker.get_reset_info()
                streams_info.append({
                    'stream_id': stream_id,
                    'auto_reset_info': reset_info
                })

        return jsonify({
            'success': True,
            'data': {
                'streams': streams_info,
                'total_streams': len(streams_info)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting global auto-reset info: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/system/auto_reset', methods=['POST'])
@require_api_key('admin')
@rate_limit(max_requests=10)
def configure_global_auto_reset():
    """Configure auto-reset settings for all streams"""
    try:
        app = get_app_module()

        data = request.get_json() or {}

        # Validate input
        if 'reset_hour' in data or 'reset_minute' in data:
            hour = data.get('reset_hour', 0)
            minute = data.get('reset_minute', 0)

            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                return jsonify({
                    'success': False,
                    'error': 'Invalid time',
                    'message': 'Hour must be 0-23, minute must be 0-59'
                }), 400

        updated_streams = []
        with app.stream_lock:
            for stream_id, stream in app.active_streams.items():
                # Configure reset time
                if 'reset_hour' in data or 'reset_minute' in data:
                    hour = data.get('reset_hour', stream.vehicle_tracker.reset_hour)
                    minute = data.get('reset_minute', stream.vehicle_tracker.reset_minute)
                    stream.vehicle_tracker.set_reset_time(hour, minute)

                # Enable/disable auto-reset
                if 'enabled' in data:
                    enabled = bool(data['enabled'])
                    stream.vehicle_tracker.enable_auto_reset(enabled)

                updated_streams.append({
                    'stream_id': stream_id,
                    'auto_reset_info': stream.vehicle_tracker.get_reset_info()
                })

        return jsonify({
            'success': True,
            'data': {
                'updated_streams': updated_streams,
                'total_updated': len(updated_streams)
            },
            'message': 'Global auto-reset configuration updated successfully'
        })

    except Exception as e:
        logger.error(f"Error configuring global auto-reset: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/system/manual_reset_all', methods=['POST'])
@require_api_key('admin')
@rate_limit(max_requests=5)
def manual_reset_all_streams():
    """Manually reset all stream statistics"""
    try:
        app = get_app_module()

        reset_streams = []
        with app.stream_lock:
            for stream_id, stream in app.active_streams.items():
                stream.vehicle_tracker.manual_reset()
                reset_streams.append(stream_id)

        # Send webhook notification
        try:
            send_webhook_notification('global_manual_reset', {
                'reset_streams': reset_streams,
                'reset_time': datetime.now().isoformat(),
                'reset_type': 'global_manual',
                'total_streams': len(reset_streams)
            })
        except Exception as e:
            logger.warning(f"Failed to send global manual reset webhook: {e}")

        return jsonify({
            'success': True,
            'data': {
                'reset_streams': reset_streams,
                'total_reset': len(reset_streams),
                'reset_time': datetime.now().isoformat()
            },
            'message': f'All {len(reset_streams)} stream statistics manually reset successfully'
        })

    except Exception as e:
        logger.error(f"Error manually resetting all streams: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# SYSTEM MONITORING API ENDPOINTS
# ============================================================================

@api_bp.route('/system/status', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=100)
def system_status():
    """Get overall system status"""
    try:
        app = get_app_module()

        with app.stream_lock:
            total_streams = len(app.active_streams)
            active_stream_count = sum(1 for stream in app.active_streams.values() if stream.is_active)

            total_detections = sum(stream.detection_stats['total_detections']
                                 for stream in app.active_streams.values())
            total_unique_vehicles = sum(stream.detection_stats['unique_vehicles']
                                      for stream in app.active_streams.values())

        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
        except ImportError:
            cpu_percent = 0
            memory = type('obj', (object,), {'total': 0, 'available': 0, 'percent': 0})
            disk = type('obj', (object,), {'total': 0, 'free': 0, 'used': 0})

        # GPU info if available
        gpu_info = None
        try:
            import torch
            if torch.cuda.is_available():
                gpu_info = {
                    'available': True,
                    'device_count': torch.cuda.device_count(),
                    'current_device': torch.cuda.current_device(),
                    'device_name': torch.cuda.get_device_name(),
                    'memory_allocated': torch.cuda.memory_allocated(),
                    'memory_reserved': torch.cuda.memory_reserved()
                }
        except:
            gpu_info = {'available': False}

        return jsonify({
            'success': True,
            'data': {
                'system': {
                    'status': 'healthy',
                    'uptime': time.time(),
                    'version': '1.0.0'
                },
                'streams': {
                    'total': total_streams,
                    'active': active_stream_count,
                    'inactive': total_streams - active_stream_count
                },
                'detections': {
                    'total_detections': total_detections,
                    'total_unique_vehicles': total_unique_vehicles
                },
                'resources': {
                    'cpu_percent': cpu_percent,
                    'memory': {
                        'total': memory.total,
                        'available': memory.available,
                        'percent': memory.percent
                    },
                    'disk': {
                        'total': disk.total,
                        'free': disk.free,
                        'percent': (disk.used / disk.total) * 100 if disk.total > 0 else 0
                    },
                    'gpu': gpu_info
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/system/health', methods=['GET'])
@rate_limit(max_requests=200)
def health_check():
    """Simple health check endpoint (no auth required)"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

# ============================================================================
# API KEY MANAGEMENT ENDPOINTS
# ============================================================================

@api_bp.route('/auth/keys', methods=['GET'])
@require_api_key('admin')
@rate_limit(max_requests=50)
def list_api_keys():
    """List all API keys (admin only)"""
    try:
        keys_info = []
        for api_key, info in api_key_manager.api_keys.items():
            keys_info.append({
                'api_key': api_key[:12] + '...',  # Masked key
                'name': info['name'],
                'permissions': info['permissions'],
                'created_at': info['created_at'].isoformat(),
                'last_used': info['last_used'].isoformat() if info['last_used'] else None,
                'usage_count': info['usage_count']
            })

        return jsonify({
            'success': True,
            'data': {
                'api_keys': keys_info,
                'total_count': len(keys_info)
            }
        })

    except Exception as e:
        logger.error(f"Error listing API keys: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/auth/keys', methods=['POST'])
@require_api_key('admin')
@rate_limit(max_requests=20)
def create_api_key():
    """Create new API key (admin only)"""
    try:
        data = request.get_json()

        if not data or 'name' not in data:
            return jsonify({
                'success': False,
                'error': 'Missing required field',
                'message': 'name is required'
            }), 400

        name = data['name']
        permissions = data.get('permissions', ['read'])

        # Validate permissions
        valid_permissions = ['read', 'write', 'admin']
        if not all(perm in valid_permissions for perm in permissions):
            return jsonify({
                'success': False,
                'error': 'Invalid permissions',
                'message': f'Valid permissions are: {valid_permissions}'
            }), 400

        api_key = api_key_manager.generate_api_key(name, permissions)

        return jsonify({
            'success': True,
            'data': {
                'api_key': api_key,
                'name': name,
                'permissions': permissions
            },
            'message': 'API key created successfully'
        }), 201

    except Exception as e:
        logger.error(f"Error creating API key: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# WEBHOOK ENDPOINTS
# ============================================================================

@api_bp.route('/webhooks', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=50)
def list_webhooks():
    """List registered webhooks"""
    try:
        webhooks_info = []
        for webhook_id, info in WEBHOOK_ENDPOINTS.items():
            webhooks_info.append({
                'webhook_id': webhook_id,
                'url': info['url'],
                'events': info['events'],
                'active': info['active'],
                'created_at': info['created_at'].isoformat()
            })

        return jsonify({
            'success': True,
            'data': {
                'webhooks': webhooks_info,
                'total_count': len(webhooks_info)
            }
        })

    except Exception as e:
        logger.error(f"Error listing webhooks: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/webhooks', methods=['POST'])
@require_api_key('write')
@rate_limit(max_requests=20)
def register_webhook():
    """Register a webhook endpoint"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({
                'success': False,
                'error': 'Missing required field',
                'message': 'url is required'
            }), 400

        webhook_url = data['url']
        events = data.get('events', ['vehicle_detected', 'stream_started', 'stream_stopped'])

        # Validate events
        valid_events = ['vehicle_detected', 'stream_started', 'stream_stopped', 'unique_vehicle_counted']
        if not all(event in valid_events for event in events):
            return jsonify({
                'success': False,
                'error': 'Invalid events',
                'message': f'Valid events are: {valid_events}'
            }), 400

        webhook_id = str(uuid.uuid4())
        WEBHOOK_ENDPOINTS[webhook_id] = {
            'url': webhook_url,
            'events': events,
            'created_at': datetime.now(),
            'active': True
        }

        return jsonify({
            'success': True,
            'data': {
                'webhook_id': webhook_id,
                'url': webhook_url,
                'events': events
            },
            'message': 'Webhook registered successfully'
        }), 201

    except Exception as e:
        logger.error(f"Error registering webhook: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/webhooks/<webhook_id>', methods=['DELETE'])
@require_api_key('write')
@rate_limit(max_requests=20)
def delete_webhook(webhook_id):
    """Delete a webhook"""
    try:
        if webhook_id not in WEBHOOK_ENDPOINTS:
            return jsonify({
                'success': False,
                'error': 'Webhook not found',
                'message': f'Webhook with ID {webhook_id} does not exist'
            }), 404

        del WEBHOOK_ENDPOINTS[webhook_id]

        return jsonify({
            'success': True,
            'message': f'Webhook {webhook_id} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting webhook {webhook_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def send_webhook_notification(event_type: str, data: Dict):
    """Send webhook notification to registered endpoints"""
    def send_notification():
        try:
            import requests
        except ImportError:
            logger.warning("requests library not available for webhook notifications")
            return

        for webhook_id, webhook_info in WEBHOOK_ENDPOINTS.items():
            if not webhook_info['active'] or event_type not in webhook_info['events']:
                continue

            try:
                payload = {
                    'event_type': event_type,
                    'timestamp': datetime.now().isoformat(),
                    'data': data
                }

                response = requests.post(
                    webhook_info['url'],
                    json=payload,
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code != 200:
                    logger.warning(f"Webhook {webhook_id} returned status {response.status_code}")

            except Exception as e:
                logger.error(f"Error sending webhook notification to {webhook_id}: {e}")

    # Send notifications in background thread
    thread = threading.Thread(target=send_notification)
    thread.daemon = True
    thread.start()

# ============================================================================
# VIDEO STREAMING API ENDPOINTS
# ============================================================================

@api_bp.route('/streams/<stream_id>/video_feed', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=50)
def api_video_feed(stream_id):
    """Stream processed video with detection overlay via API"""
    try:
        app = get_app_module()

        # Ensure stream exists in this instance; resurrect if possible
        with app.stream_lock:
            if stream_id not in app.active_streams:
                # Try to resurrect stream if client provides stream_url
                stream_url = request.args.get('stream_url') or request.headers.get('X-Stream-URL')
                # If not provided by client, attempt to fetch from DB by stream_id
                if not stream_url:
                    try:
                        if 'db_get_stream' in globals():
                            row = db_get_stream(stream_id)
                            if row and row.get('status') != 'deleted':
                                stream_url = row.get('stream_url')
                    except Exception as e:
                        logger.warning(f"DB lookup failed for stream {stream_id}: {e}")
                if stream_url:
                    try:
                        is_valid, msg = app.validate_stream_url(stream_url)
                        if not is_valid:
                            return jsonify({
                                'success': False,
                                'error': 'Invalid stream URL',
                                'message': msg
                            }), 400
                        cctv_stream = app.CCTVStream(stream_url, stream_id)
                        if cctv_stream.connect_stream():
                            app.active_streams[stream_id] = cctv_stream
                            logger.info(f"Resurrected stream {stream_id} on this instance")
                        else:
                            return jsonify({
                                'success': False,
                                'error': 'Stream not found',
                                'message': f'Stream {stream_id} not active on this instance and failed to connect to provided URL'
                            }), 404
                    except Exception as e:
                        logger.error(f"Failed to resurrect stream {stream_id}: {e}")
                        return jsonify({
                            'success': False,
                            'error': 'Stream not found',
                            'message': f'Stream with ID {stream_id} does not exist'
                        }), 404
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Stream not found',
                        'message': f'Stream with ID {stream_id} does not exist'
                    }), 404

            stream = app.active_streams[stream_id]

        # Do not fail if inactive; generator will attempt reconnection with keep-alive

        def generate_api_frames():
            """Generate video frames for API streaming with keep-alive and robust reconnection"""
            frame_count = 0
            keepalive_interval = 5.0
            last_keepalive = 0.0
            keepalive_jpeg = None

            def get_keepalive_jpeg():
                nonlocal keepalive_jpeg
                if keepalive_jpeg is None:
                    img = np.zeros((240, 320, 3), dtype=np.uint8)
                    cv2.putText(img, "Reconnecting...", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    ok, buf = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                    keepalive_jpeg = buf.tobytes() if ok else b''
                return keepalive_jpeg

            while True:
                with app.stream_lock:
                    if stream_id not in app.active_streams:
                        logger.info(f"API stream {stream_id} not found in active streams")
                        break
                    current_stream = app.active_streams[stream_id]

                if not current_stream.is_active:
                    logger.info(f"API stream {stream_id} is not active, attempting reconnection")
                    try:
                        current_stream.disconnect_stream()
                    except Exception:
                        pass
                    backoff = 1.0
                    while True:
                        with app.stream_lock:
                            if stream_id not in app.active_streams:
                                logger.info(f"API stream {stream_id} removed during reconnection loop")
                                return
                            current_stream = app.active_streams[stream_id]
                        if current_stream.connect_stream():
                            logger.info("Successfully reconnected API stream")
                            break
                        now = time.time()
                        if now - last_keepalive >= keepalive_interval:
                            last_keepalive = now
                            ka = get_keepalive_jpeg()
                            if ka:
                                yield (b'--frame\r\n'
                                       b'Content-Type: image/jpeg\r\n'
                                       b'X-Status: reconnecting\r\n\r\n' + ka + b'\r\n')
                        time.sleep(backoff)
                        backoff = min(backoff * 2, 15.0)

                try:
                    frame = current_stream.process_frame()
                    if frame is not None:
                        frame_count += 1
                        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        else:
                            logger.warning("Failed to encode frame for API stream")
                    else:
                        logger.warning("Received None frame in API stream, attempting reconnection with backoff")
                        try:
                            current_stream.disconnect_stream()
                        except Exception:
                            pass
                        backoff = 1.0
                        while True:
                            with app.stream_lock:
                                if stream_id not in app.active_streams:
                                    logger.info(f"API stream {stream_id} removed during reconnection loop")
                                    return
                                current_stream = app.active_streams[stream_id]
                            if current_stream.connect_stream():
                                logger.info("Successfully reconnected API stream")
                                break
                            now = time.time()
                            if now - last_keepalive >= keepalive_interval:
                                last_keepalive = now
                                ka = get_keepalive_jpeg()
                                if ka:
                                    yield (b'--frame\r\n'
                                           b'Content-Type: image/jpeg\r\n'
                                           b'X-Status: reconnecting\r\n\r\n' + ka + b'\r\n')
                            time.sleep(backoff)
                            backoff = min(backoff * 2, 15.0)

                except Exception as e:
                    logger.error(f"Error in API frame generation: {e}")
                    now = time.time()
                    if now - last_keepalive >= keepalive_interval:
                        last_keepalive = now
                        ka = get_keepalive_jpeg()
                        if ka:
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n'
                                   b'X-Status: error\r\n\r\n' + ka + b'\r\n')
                    time.sleep(1.0)
                    continue

                time.sleep(0.033)

            logger.info(f"API frame generation stopped for stream {stream_id} after {frame_count} frames")

        return Response(
            generate_api_frames(),
            mimetype='multipart/x-mixed-replace; boundary=frame',
            headers={
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',
                'X-Stream-ID': stream_id,
                'X-Stream-URL': stream.stream_url
            }
        )

    except Exception as e:
        logger.error(f"Error in API video feed for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/frame', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=100)
def get_current_frame(stream_id):
    """Get current frame as base64 encoded image"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        if not stream.is_active:
            return jsonify({
                'success': False,
                'error': 'Stream inactive',
                'message': 'Stream is not currently active'
            }), 400

        # Get current frame
        frame = stream.process_frame()
        if frame is None:
            return jsonify({
                'success': False,
                'error': 'No frame available',
                'message': 'Unable to capture current frame'
            }), 404

        # Encode frame as base64
        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
        if not ret:
            return jsonify({
                'success': False,
                'error': 'Frame encoding failed',
                'message': 'Unable to encode frame'
            }), 500

        frame_base64 = base64.b64encode(buffer).decode('utf-8')

        # Get current detections for metadata
        active_vehicles = stream.vehicle_tracker.get_active_vehicles()
        detections = []
        for vehicle_id, vehicle_info in active_vehicles:
            x1, y1, x2, y2 = vehicle_info['bbox']
            detections.append({
                'vehicle_id': vehicle_id,
                'class_id': vehicle_info['class_id'],
                'confidence': float(vehicle_info['confidence']),
                'bbox': {
                    'x1': float(x1), 'y1': float(y1),
                    'x2': float(x2), 'y2': float(y2)
                }
            })

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'frame_base64': frame_base64,
                'frame_count': stream.frame_count,
                'timestamp': datetime.now().isoformat(),
                'detections': detections,
                'detection_count': len(detections),
                'image_format': 'jpeg',
                'encoding': 'base64'
            }
        })

    except Exception as e:
        logger.error(f"Error getting current frame for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/stream_info', methods=['GET'])
@require_api_key('read')
@rate_limit(max_requests=200)
def get_stream_info(stream_id):
    """Get detailed stream information including capabilities"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        # Get stream capabilities
        capabilities = {
            'video_streaming': True,
            'frame_capture': True,
            'real_time_detection': True,
            'vehicle_tracking': True,
            'statistics': True,
            'webhook_notifications': True
        }

        # Get stream endpoints
        base_url = request.url_root.rstrip('/')
        endpoints = {
            'video_feed': f"{base_url}/api/v1/streams/{stream_id}/video_feed",
            'current_frame': f"{base_url}/api/v1/streams/{stream_id}/frame",
            'detections': f"{base_url}/api/v1/streams/{stream_id}/detections",
            'statistics': f"{base_url}/api/v1/streams/{stream_id}/statistics",
            'stream_info': f"{base_url}/api/v1/streams/{stream_id}/stream_info"
        }

        # Get current stream status
        tracking_stats = stream.vehicle_tracker.get_statistics()

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'stream_url': stream.stream_url,
                'is_active': stream.is_active,
                'frame_count': stream.frame_count,
                'capabilities': capabilities,
                'endpoints': endpoints,
                'current_stats': {
                    'total_detections': stream.detection_stats['total_detections'],
                    'unique_vehicles': tracking_stats['total_unique'],
                    'currently_tracked': tracking_stats['currently_tracked'],
                    'by_type': tracking_stats['by_type']
                },
                'stream_format': 'MJPEG',
                'detection_classes': {
                    1: 'Sepeda',
                    2: 'Mobil',
                    3: 'Motor',
                    5: 'Bus',
                    7: 'Truk'
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting stream info for {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# STREAM SHARING AND ACCESS CONTROL
# ============================================================================

@api_bp.route('/streams/<stream_id>/share', methods=['POST'])
@require_api_key('write')
@rate_limit(max_requests=20)
def share_stream(stream_id):
    """Share a stream publicly or with specific API keys"""
    try:
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

        data = request.get_json() or {}
        share_type = data.get('share_type', 'public')  # 'public' or 'private'
        allowed_keys = data.get('allowed_keys', [])  # List of API keys for private sharing
        expires_at = data.get('expires_at')  # Optional expiration time

        if share_type not in ['public', 'private']:
            return jsonify({
                'success': False,
                'error': 'Invalid share type',
                'message': 'share_type must be "public" or "private"'
            }), 400

        # Create share configuration
        share_config = {
            'stream_id': stream_id,
            'share_type': share_type,
            'allowed_keys': allowed_keys if share_type == 'private' else [],
            'created_at': datetime.now(),
            'expires_at': datetime.fromisoformat(expires_at) if expires_at else None,
            'created_by': request.headers.get('X-API-Key', '')[:12] + '...',
            'access_count': 0
        }

        SHARED_STREAMS[stream_id] = share_config

        # Generate share URLs
        base_url = request.url_root.rstrip('/')
        share_urls = {
            'video_feed': f"{base_url}/api/v1/shared/streams/{stream_id}/video_feed",
            'current_frame': f"{base_url}/api/v1/shared/streams/{stream_id}/frame",
            'stream_info': f"{base_url}/api/v1/shared/streams/{stream_id}/info"
        }

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'share_type': share_type,
                'share_urls': share_urls,
                'allowed_keys': allowed_keys if share_type == 'private' else None,
                'expires_at': expires_at,
                'share_token': stream_id  # For simplicity, using stream_id as token
            },
            'message': f'Stream shared successfully as {share_type}'
        }), 201

    except Exception as e:
        logger.error(f"Error sharing stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/streams/<stream_id>/unshare', methods=['DELETE'])
@require_api_key('write')
@rate_limit(max_requests=20)
def unshare_stream(stream_id):
    """Stop sharing a stream"""
    try:
        if stream_id not in SHARED_STREAMS:
            return jsonify({
                'success': False,
                'error': 'Stream not shared',
                'message': f'Stream {stream_id} is not currently shared'
            }), 404

        del SHARED_STREAMS[stream_id]

        return jsonify({
            'success': True,
            'message': f'Stream {stream_id} is no longer shared'
        })

    except Exception as e:
        logger.error(f"Error unsharing stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/shared/streams', methods=['GET'])
@rate_limit(max_requests=100)
def list_shared_streams():
    """List all publicly shared streams (no auth required for public streams)"""
    try:
        api_key = request.headers.get('X-API-Key')
        public_streams = []
        accessible_streams = []

        for stream_id, config in SHARED_STREAMS.items():
            # Check if share has expired
            if config['expires_at'] and datetime.now() > config['expires_at']:
                continue

            stream_info = {
                'stream_id': stream_id,
                'share_type': config['share_type'],
                'created_at': config['created_at'].isoformat(),
                'expires_at': config['expires_at'].isoformat() if config['expires_at'] else None,
                'access_count': config['access_count']
            }

            if config['share_type'] == 'public':
                public_streams.append(stream_info)
            elif config['share_type'] == 'private' and api_key:
                # Check if API key has access
                if api_key in config['allowed_keys'] or api_key_manager.validate_api_key(api_key, 'read'):
                    accessible_streams.append(stream_info)

        return jsonify({
            'success': True,
            'data': {
                'public_streams': public_streams,
                'accessible_streams': accessible_streams if api_key else [],
                'total_public': len(public_streams),
                'total_accessible': len(accessible_streams)
            }
        })

    except Exception as e:
        logger.error(f"Error listing shared streams: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

def check_stream_access(stream_id, api_key=None):
    """Check if access is allowed to a shared stream"""
    if stream_id not in SHARED_STREAMS:
        return False, "Stream not shared"

    config = SHARED_STREAMS[stream_id]

    # Check expiration
    if config['expires_at'] and datetime.now() > config['expires_at']:
        return False, "Share has expired"

    # Public streams are accessible to everyone
    if config['share_type'] == 'public':
        return True, "Public access"

    # Private streams require API key
    if config['share_type'] == 'private':
        if not api_key:
            return False, "API key required for private stream"

        if api_key in config['allowed_keys']:
            return True, "Authorized access"

        # Check if it's a valid API key with read permission
        if api_key_manager.validate_api_key(api_key, 'read'):
            return True, "Valid API key access"

        return False, "Access denied"

    return False, "Unknown share type"

# ============================================================================
# SHARED STREAM ENDPOINTS (PUBLIC ACCESS)
# ============================================================================

@api_bp.route('/shared/streams/<stream_id>/video_feed', methods=['GET'])
@rate_limit(max_requests=50)
def shared_video_feed(stream_id):
    """Access shared video stream (public or with API key)"""
    try:
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')

        # Check access permission
        has_access, message = check_stream_access(stream_id, api_key)
        if not has_access:
            return jsonify({
                'success': False,
                'error': 'Access denied',
                'message': message
            }), 403

        # Update access count
        if stream_id in SHARED_STREAMS:
            SHARED_STREAMS[stream_id]['access_count'] += 1

        # Get the stream
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found',
                    'message': f'Stream with ID {stream_id} does not exist'
                }), 404

            stream = app.active_streams[stream_id]

        if not stream.is_active:
            return jsonify({
                'success': False,
                'error': 'Stream inactive',
                'message': 'Stream is not currently active'
            }), 400

        def generate_shared_frames():
            """Generate video frames for shared streaming with keep-alive and robust reconnection"""
            frame_count = 0
            keepalive_interval = 5.0
            last_keepalive = 0.0
            keepalive_jpeg = None

            def get_keepalive_jpeg():
                nonlocal keepalive_jpeg
                if keepalive_jpeg is None:
                    img = np.zeros((240, 320, 3), dtype=np.uint8)
                    cv2.putText(img, "Reconnecting...", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    ok, buf = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                    keepalive_jpeg = buf.tobytes() if ok else b''
                return keepalive_jpeg

            while True:
                with app.stream_lock:
                    if stream_id not in app.active_streams:
                        break
                    current_stream = app.active_streams[stream_id]

                if not current_stream.is_active:
                    # attempt reconnection with backoff while keeping client alive
                    try:
                        current_stream.disconnect_stream()
                    except Exception:
                        pass
                    backoff = 1.0
                    while True:
                        with app.stream_lock:
                            if stream_id not in app.active_streams:
                                return
                            current_stream = app.active_streams[stream_id]
                        if current_stream.connect_stream():
                            break
                        now = time.time()
                        if now - last_keepalive >= keepalive_interval:
                            last_keepalive = now
                            ka = get_keepalive_jpeg()
                            if ka:
                                yield (b'--frame\r\n'
                                       b'Content-Type: image/jpeg\r\n'
                                       b'X-Status: reconnecting\r\n\r\n' + ka + b'\r\n')
                        time.sleep(backoff)
                        backoff = min(backoff * 2, 15.0)

                try:
                    frame = current_stream.process_frame()
                    if frame is not None:
                        frame_count += 1
                        # Add watermark for shared streams
                        cv2.putText(frame, "SHARED STREAM", (10, frame.shape[0] - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                        # Encode frame as JPEG
                        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    else:
                        # Reconnect with keepalive
                        try:
                            current_stream.disconnect_stream()
                        except Exception:
                            pass
                        backoff = 1.0
                        while True:
                            with app.stream_lock:
                                if stream_id not in app.active_streams:
                                    return
                                current_stream = app.active_streams[stream_id]
                            if current_stream.connect_stream():
                                break
                            now = time.time()
                            if now - last_keepalive >= keepalive_interval:
                                last_keepalive = now
                                ka = get_keepalive_jpeg()
                                if ka:
                                    yield (b'--frame\r\n'
                                           b'Content-Type: image/jpeg\r\n'
                                           b'X-Status: reconnecting\r\n\r\n' + ka + b'\r\n')
                            time.sleep(backoff)
                            backoff = min(backoff * 2, 15.0)

                except Exception as e:
                    logger.error(f"Error in shared frame generation: {e}")
                    now = time.time()
                    if now - last_keepalive >= keepalive_interval:
                        last_keepalive = now
                        ka = get_keepalive_jpeg()
                        if ka:
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n'
                                   b'X-Status: error\r\n\r\n' + ka + b'\r\n')
                    time.sleep(1.0)
                    continue

                time.sleep(0.033)  # ~30 FPS

        return Response(
            generate_shared_frames(),
            mimetype='multipart/x-mixed-replace; boundary=frame',
            headers={
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',
                'X-Stream-ID': stream_id,
                'X-Access-Type': 'shared'
            }
        )

    except Exception as e:
        logger.error(f"Error in shared video feed for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/shared/streams/<stream_id>/frame', methods=['GET'])
@rate_limit(max_requests=100)
def shared_current_frame(stream_id):
    """Get current frame from shared stream"""
    try:
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')

        # Check access permission
        has_access, message = check_stream_access(stream_id, api_key)
        if not has_access:
            return jsonify({
                'success': False,
                'error': 'Access denied',
                'message': message
            }), 403

        # Update access count
        if stream_id in SHARED_STREAMS:
            SHARED_STREAMS[stream_id]['access_count'] += 1

        # Get the stream
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found'
                }), 404

            stream = app.active_streams[stream_id]

        if not stream.is_active:
            return jsonify({
                'success': False,
                'error': 'Stream inactive'
            }), 400

        # Get current frame
        frame = stream.process_frame()
        if frame is None:
            return jsonify({
                'success': False,
                'error': 'No frame available'
            }), 404

        # Add watermark for shared streams
        cv2.putText(frame, "SHARED STREAM", (10, frame.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # Encode frame as base64
        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
        if not ret:
            return jsonify({
                'success': False,
                'error': 'Frame encoding failed'
            }), 500

        frame_base64 = base64.b64encode(buffer).decode('utf-8')

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'frame_base64': frame_base64,
                'frame_count': stream.frame_count,
                'timestamp': datetime.now().isoformat(),
                'access_type': 'shared',
                'image_format': 'jpeg',
                'encoding': 'base64'
            }
        })

    except Exception as e:
        logger.error(f"Error getting shared frame for stream {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@api_bp.route('/shared/streams/<stream_id>/info', methods=['GET'])
@rate_limit(max_requests=200)
def shared_stream_info(stream_id):
    """Get information about shared stream"""
    try:
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')

        # Check access permission
        has_access, message = check_stream_access(stream_id, api_key)
        if not has_access:
            return jsonify({
                'success': False,
                'error': 'Access denied',
                'message': message
            }), 403

        # Get share config
        if stream_id not in SHARED_STREAMS:
            return jsonify({
                'success': False,
                'error': 'Stream not shared'
            }), 404

        share_config = SHARED_STREAMS[stream_id]

        # Get stream info
        app = get_app_module()

        with app.stream_lock:
            if stream_id not in app.active_streams:
                return jsonify({
                    'success': False,
                    'error': 'Stream not found'
                }), 404

            stream = app.active_streams[stream_id]

        # Get basic statistics (limited for shared access)
        tracking_stats = stream.vehicle_tracker.get_statistics()

        # Generate share URLs
        base_url = request.url_root.rstrip('/')
        share_urls = {
            'video_feed': f"{base_url}/api/v1/shared/streams/{stream_id}/video_feed",
            'current_frame': f"{base_url}/api/v1/shared/streams/{stream_id}/frame",
            'stream_info': f"{base_url}/api/v1/shared/streams/{stream_id}/info"
        }

        return jsonify({
            'success': True,
            'data': {
                'stream_id': stream_id,
                'is_active': stream.is_active,
                'share_type': share_config['share_type'],
                'access_count': share_config['access_count'],
                'expires_at': share_config['expires_at'].isoformat() if share_config['expires_at'] else None,
                'share_urls': share_urls,
                'capabilities': {
                    'video_streaming': True,
                    'frame_capture': True,
                    'basic_stats': True
                },
                'current_stats': {
                    'unique_vehicles': tracking_stats['total_unique'],
                    'currently_tracked': tracking_stats['currently_tracked']
                },
                'detection_classes': {
                    1: 'Sepeda',
                    2: 'Mobil',
                    3: 'Motor',
                    5: 'Bus',
                    7: 'Truk'
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting shared stream info for {stream_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500

# ============================================================================
# ERROR HANDLERS
# ============================================================================

@api_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Not found',
        'message': 'The requested resource was not found'
    }), 404

@api_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method not allowed',
        'message': 'The requested method is not allowed for this resource'
    }), 405

@api_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500
