const { MarineTrafficFullScraper } = require('./marine-scraper-full');
const { cleanDuplicateData } = require('./clean-duplicate-data');
const fs = require('fs').promises;
const path = require('path');

/**
 * Complete Marine Traffic Scraper
 * Mengambil semua data, <PERSON><PERSON><PERSON> duplikasi, dan <PERSON> format seperti download.json
 */
class CompleteMarineTrafficScraper {
    constructor(options = {}) {
        this.options = {
            headless: true,
            pageSize: 500,
            maxPages: 25,
            delay: 3000,
            outputPath: path.join(__dirname, `marine-traffic-complete-${Date.now()}.json`),
            keepTempFiles: false,
            ...options
        };
    }

    async run() {
        let tempFullFile = null;
        
        try {
            console.log('🌊 Starting Complete Marine Traffic Scraper...\n');
            
            // Step 1: Scrape all data with pagination
            console.log('📊 Step 1: Scraping all data with pagination...');
            const fullScraper = new MarineTrafficFullScraper({
                headless: this.options.headless,
                pageSize: this.options.pageSize,
                maxPages: this.options.maxPages,
                delay: this.options.delay,
                outputPath: path.join(__dirname, `temp-full-${Date.now()}.json`)
            });
            
            const fullData = await fullScraper.run();
            tempFullFile = fullScraper.options.outputPath;
            
            console.log(`✅ Step 1 completed: ${fullData.collectedCount} vessels collected\n`);
            
            // Step 2: Clean duplicates and format data
            console.log('🧹 Step 2: Cleaning duplicates and formatting data...');
            const cleanData = await cleanDuplicateData(tempFullFile, this.options.outputPath);
            
            console.log(`✅ Step 2 completed: ${cleanData.data.length} unique vessels\n`);
            
            // Step 3: Generate final statistics
            console.log('📈 Step 3: Generating final statistics...');
            const stats = this.generateStatistics(cleanData.data);
            
            console.log('🎉 Complete scraping finished successfully!\n');
            console.log('📊 Final Results:');
            console.log(`   📁 Output file: ${this.options.outputPath}`);
            console.log(`   🚢 Total unique vessels: ${cleanData.data.length}`);
            console.log(`   🏳️  Countries represented: ${stats.countries.length}`);
            console.log(`   ⚓ Ship types: ${stats.shipTypes.length}`);
            console.log(`   🇮🇩 Indonesian vessels: ${stats.indonesianVessels}`);
            console.log(`   🌍 Foreign vessels: ${stats.foreignVessels}`);
            
            console.log('\n🏳️  Top 5 Countries:');
            stats.topCountries.slice(0, 5).forEach(([country, count], index) => {
                console.log(`   ${index + 1}. ${country}: ${count} vessels`);
            });
            
            console.log('\n🚢 Ship Types Distribution:');
            stats.topShipTypes.forEach(([type, count]) => {
                console.log(`   ${type}: ${count} vessels`);
            });
            
            // Step 4: Cleanup temp files
            if (!this.options.keepTempFiles && tempFullFile) {
                try {
                    await fs.unlink(tempFullFile);
                    console.log('\n🗑️  Temporary files cleaned up');
                } catch (error) {
                    console.log('\n⚠️  Could not clean up temporary files');
                }
            }
            
            return {
                data: cleanData,
                statistics: stats,
                outputPath: this.options.outputPath
            };
            
        } catch (error) {
            console.error('\n❌ Complete scraper failed:', error.message);
            
            // Cleanup on error
            if (tempFullFile) {
                try {
                    await fs.unlink(tempFullFile);
                } catch (cleanupError) {
                    // Ignore cleanup errors
                }
            }
            
            throw error;
        }
    }

    generateStatistics(vessels) {
        const countryStats = {};
        const typeStats = {};
        let indonesianVessels = 0;
        let foreignVessels = 0;

        vessels.forEach(vessel => {
            // Country statistics
            const country = vessel.COUNTRY || 'Unknown';
            countryStats[country] = (countryStats[country] || 0) + 1;
            
            if (country === 'Indonesia') {
                indonesianVessels++;
            } else {
                foreignVessels++;
            }
            
            // Type statistics
            const type = vessel.TYPE_SUMMARY || 'Unknown';
            typeStats[type] = (typeStats[type] || 0) + 1;
        });

        return {
            countries: Object.keys(countryStats),
            shipTypes: Object.keys(typeStats),
            indonesianVessels,
            foreignVessels,
            topCountries: Object.entries(countryStats).sort(([,a], [,b]) => b - a),
            topShipTypes: Object.entries(typeStats).sort(([,a], [,b]) => b - a)
        };
    }
}

/**
 * Quick run function
 */
async function quickRunComplete(options = {}) {
    const scraper = new CompleteMarineTrafficScraper(options);
    return await scraper.run();
}

// Export for module use
module.exports = { CompleteMarineTrafficScraper, quickRunComplete };

// Run if called directly
if (require.main === module) {
    const args = process.argv.slice(2);
    
    const options = {
        headless: true,
        pageSize: 500,
        maxPages: 25,
        delay: 3000,
        keepTempFiles: args.includes('--keep-temp'),
        outputPath: args.find(arg => arg.startsWith('--output='))?.split('=')[1] || 
                   path.join(__dirname, `marine-traffic-complete-${Date.now()}.json`)
    };
    
    console.log('🚀 Marine Traffic Complete Scraper');
    console.log('=====================================\n');
    
    if (args.includes('--help')) {
        console.log(`
Usage: node marine-scraper-complete.js [options]

Options:
  --output=<file>    Specify output file path
  --keep-temp        Keep temporary files after completion
  --help             Show this help message

Examples:
  node marine-scraper-complete.js
  node marine-scraper-complete.js --output=my-data.json
  node marine-scraper-complete.js --keep-temp

This script will:
1. Scrape all marine traffic data using pagination
2. Remove duplicate vessels based on SHIP_ID
3. Generate clean data in the same format as download.json
4. Provide detailed statistics about the collected data
        `);
        process.exit(0);
    }
    
    quickRunComplete(options)
        .then((result) => {
            console.log('\n✨ All done! Your complete marine traffic data is ready.');
            console.log(`📁 File: ${result.outputPath}`);
            console.log(`🚢 Vessels: ${result.data.data.length}`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Complete scraping failed:', error.message);
            process.exit(1);
        });
}
