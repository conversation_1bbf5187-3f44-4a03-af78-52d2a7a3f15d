const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

/**
 * Marine Traffic Full Data Scraper
 * Mengambil SEMUA data kapal menggunakan pagination
 */
class MarineTrafficFullScraper {
    constructor(options = {}) {
        this.options = {
            headless: true,
            sessionPath: path.join(__dirname, 'sessions', 'session.json'),
            outputPath: path.join(__dirname, 'marine-traffic-full-data.json'),
            timeout: 30000,
            pageSize: 500, // Ukuran per halaman
            maxPages: 50,   // Maksimal halaman untuk safety
            delay: 2000,    // Delay antar request (ms)
            ...options
        };
        
        this.browser = null;
        this.context = null;
        this.page = null;
    }

    /**
     * Initialize browser and load session
     */
    async init() {
        try {
            console.log('🚀 Initializing Marine Traffic Full Scraper...');
            
            // Launch browser
            this.browser = await chromium.launch({
                headless: this.options.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            // Create context
            this.context = await this.browser.newContext({
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
                viewport: { width: 1920, height: 1080 },
                locale: 'en-US',
                timezoneId: 'Asia/Jakarta'
            });

            // Load session cookies
            await this.loadSession();

            // Create page
            this.page = await this.context.newPage();

            // Set headers
            await this.page.setExtraHTTPHeaders({
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest'
            });

            console.log('✅ Browser initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing browser:', error.message);
            throw error;
        }
    }

    /**
     * Load session cookies from file
     */
    async loadSession() {
        try {
            const sessionData = await fs.readFile(this.options.sessionPath, 'utf8');
            const cookies = JSON.parse(sessionData);
            
            await this.context.addCookies(cookies);
            console.log(`🍪 Loaded ${cookies.length} cookies from session`);
        } catch (error) {
            console.error('❌ Error loading session:', error.message);
            throw error;
        }
    }

    /**
     * Fetch single page of data
     */
    async fetchPage(page = 1) {
        try {
            const offset = (page - 1) * this.options.pageSize;
            const apiUrl = `https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in&offset=${offset}&limit=${this.options.pageSize}`;
            
            console.log(`📄 Fetching page ${page} (offset: ${offset})...`);
            
            const response = await this.page.goto(apiUrl, {
                waitUntil: 'networkidle',
                timeout: this.options.timeout
            });

            if (!response.ok()) {
                throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
            }

            // Get response content
            const responseText = await this.page.content();
            
            // Extract JSON from HTML response
            let data;
            if (responseText.includes('<pre>') && responseText.includes('</pre>')) {
                const jsonMatch = responseText.match(/<pre>(.*?)<\/pre>/s);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[1]);
                } else {
                    throw new Error('Could not extract JSON from HTML');
                }
            } else {
                data = JSON.parse(responseText);
            }

            console.log(`✅ Page ${page}: Found ${data.data ? data.data.length : 0} vessels`);
            return data;

        } catch (error) {
            console.error(`❌ Error fetching page ${page}:`, error.message);
            throw error;
        }
    }

    /**
     * Fetch all data using pagination
     */
    async fetchAllData() {
        try {
            console.log('🌐 Starting to fetch all marine traffic data...');
            
            let allVessels = [];
            let totalCount = 0;
            let page = 1;
            let hasMoreData = true;

            while (hasMoreData && page <= this.options.maxPages) {
                const pageData = await this.fetchPage(page);
                
                if (pageData && pageData.data && Array.isArray(pageData.data)) {
                    allVessels = allVessels.concat(pageData.data);
                    totalCount = pageData.totalCount || totalCount;
                    
                    console.log(`📊 Progress: ${allVessels.length}/${totalCount} vessels collected`);
                    
                    // Check if we have more data
                    if (pageData.data.length < this.options.pageSize) {
                        hasMoreData = false;
                        console.log('✅ Reached end of data');
                    }
                } else {
                    hasMoreData = false;
                    console.log('❌ No more data available');
                }

                // Delay between requests to be respectful
                if (hasMoreData && page < this.options.maxPages) {
                    console.log(`⏳ Waiting ${this.options.delay}ms before next request...`);
                    await new Promise(resolve => setTimeout(resolve, this.options.delay));
                }

                page++;
            }

            const result = {
                data: allVessels,
                totalCount: totalCount,
                collectedCount: allVessels.length,
                timestamp: new Date().toISOString()
            };

            console.log(`\n🎉 Data collection completed!`);
            console.log(`📊 Total vessels collected: ${allVessels.length}`);
            console.log(`📈 Total available: ${totalCount}`);
            console.log(`📄 Pages fetched: ${page - 1}`);

            return result;

        } catch (error) {
            console.error('❌ Error fetching all data:', error.message);
            throw error;
        }
    }

    /**
     * Save data to file
     */
    async saveData(data) {
        try {
            await fs.writeFile(this.options.outputPath, JSON.stringify(data, null, 2), 'utf8');
            console.log(`💾 Data saved to: ${this.options.outputPath}`);
        } catch (error) {
            console.error('❌ Error saving data:', error.message);
            throw error;
        }
    }

    /**
     * Close browser
     */
    async close() {
        try {
            if (this.page) await this.page.close();
            if (this.context) await this.context.close();
            if (this.browser) await this.browser.close();
            console.log('🧹 Browser closed successfully');
        } catch (error) {
            console.error('❌ Error closing browser:', error.message);
        }
    }

    /**
     * Main execution method
     */
    async run() {
        try {
            await this.init();
            const data = await this.fetchAllData();
            await this.saveData(data);
            
            console.log('\n📊 Final Summary:');
            console.log(`   Total vessels collected: ${data.collectedCount}`);
            console.log(`   Total available: ${data.totalCount}`);
            console.log(`   Coverage: ${((data.collectedCount / data.totalCount) * 100).toFixed(1)}%`);
            console.log(`   Data timestamp: ${data.timestamp}`);
            console.log(`   Output file: ${this.options.outputPath}`);
            
            return data;
            
        } catch (error) {
            console.error('❌ Full scraper execution failed:', error.message);
            throw error;
        } finally {
            await this.close();
        }
    }
}

/**
 * Quick run function
 */
async function quickRunFull(options = {}) {
    const scraper = new MarineTrafficFullScraper(options);
    return await scraper.run();
}

// Export for module use
module.exports = { MarineTrafficFullScraper, quickRunFull };

// Run if called directly
if (require.main === module) {
    const options = {
        headless: true,
        pageSize: 500,
        maxPages: 25, // Maksimal 25 halaman = 12,500 vessels
        delay: 3000,  // 3 detik delay antar request
        outputPath: path.join(__dirname, `marine-traffic-full-${Date.now()}.json`)
    };
    
    quickRunFull(options)
        .then((data) => {
            console.log('\n🎉 Full scraping completed successfully!');
            console.log(`📊 Collected ${data.collectedCount} out of ${data.totalCount} vessels`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Full scraping failed:', error.message);
            process.exit(1);
        });
}
