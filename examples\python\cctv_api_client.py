#!/usr/bin/env python3
"""
CCTV Monitoring API Client - Python Example
==========================================

This example demonstrates how to interact with the CCTV Monitoring API using Python.
"""

import requests
import json
import time
from typing import Dict, List, Optional

class CCTVAPIClient:
    """Python client for CCTV Monitoring API"""
    
    def __init__(self, base_url: str, api_key: str):
        """
        Initialize the API client
        
        Args:
            base_url: Base URL of the CCTV API (e.g., 'http://localhost:5000/api/v1')
            api_key: Your API key
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Make HTTP request to API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response text: {e.response.text}")
            raise
    
    # Stream Management Methods
    
    def list_streams(self) -> List[Dict]:
        """List all active streams"""
        response = self._make_request('GET', '/streams')
        return response['data']['streams']
    
    def create_stream(self, stream_url: str, name: Optional[str] = None) -> Dict:
        """Create and start a new stream"""
        data = {'stream_url': stream_url}
        if name:
            data['name'] = name
        
        response = self._make_request('POST', '/streams', data)
        return response['data']
    
    def get_stream(self, stream_id: str) -> Dict:
        """Get specific stream information"""
        response = self._make_request('GET', f'/streams/{stream_id}')
        return response['data']
    
    def delete_stream(self, stream_id: str) -> bool:
        """Stop and delete a stream"""
        response = self._make_request('DELETE', f'/streams/{stream_id}')
        return response['success']
    
    # Detection Methods
    
    def get_detections(self, stream_id: str) -> List[Dict]:
        """Get current detections for a stream"""
        response = self._make_request('GET', f'/streams/{stream_id}/detections')
        return response['data']['detections']
    
    def get_statistics(self, stream_id: str) -> Dict:
        """Get detailed statistics for a stream"""
        response = self._make_request('GET', f'/streams/{stream_id}/statistics')
        return response['data']
    
    def reset_statistics(self, stream_id: str) -> bool:
        """Reset statistics for a stream"""
        response = self._make_request('DELETE', f'/streams/{stream_id}/statistics')
        return response['success']
    
    # System Methods
    
    def get_system_status(self) -> Dict:
        """Get overall system status"""
        response = self._make_request('GET', '/system/status')
        return response['data']
    
    def health_check(self) -> Dict:
        """Simple health check"""
        response = self._make_request('GET', '/system/health')
        return response
    
    # Webhook Methods
    
    def list_webhooks(self) -> List[Dict]:
        """List registered webhooks"""
        response = self._make_request('GET', '/webhooks')
        return response['data']['webhooks']
    
    def register_webhook(self, url: str, events: List[str]) -> Dict:
        """Register a webhook endpoint"""
        data = {'url': url, 'events': events}
        response = self._make_request('POST', '/webhooks', data)
        return response['data']
    
    def delete_webhook(self, webhook_id: str) -> bool:
        """Delete a webhook"""
        response = self._make_request('DELETE', f'/webhooks/{webhook_id}')
        return response['success']

# Example Usage
def main():
    """Example usage of the CCTV API client"""
    
    # Initialize client
    client = CCTVAPIClient(
        base_url='http://localhost:5000/api/v1',
        api_key='your-api-key-here'
    )
    
    try:
        # Health check
        print("🔍 Checking system health...")
        health = client.health_check()
        print(f"✅ System status: {health['status']}")
        
        # Get system status
        print("\n📊 Getting system status...")
        status = client.get_system_status()
        print(f"📈 Active streams: {status['streams']['active']}")
        print(f"🚗 Total detections: {status['detections']['total_detections']}")
        
        # Create a new stream
        print("\n🎥 Creating new stream...")
        stream_url = "http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8"
        stream = client.create_stream(stream_url, "Test Stream")
        stream_id = stream['stream_id']
        print(f"✅ Stream created: {stream_id}")
        
        # Wait a bit for stream to start processing
        print("\n⏳ Waiting for stream to process...")
        time.sleep(10)
        
        # Get detections
        print("\n🔍 Getting current detections...")
        detections = client.get_detections(stream_id)
        print(f"🚗 Current detections: {len(detections)}")
        
        for detection in detections[:3]:  # Show first 3 detections
            print(f"  - {detection['vehicle_type']}: confidence {detection['confidence']:.2f}")
        
        # Get statistics
        print("\n📊 Getting stream statistics...")
        stats = client.get_statistics(stream_id)
        print(f"📈 Frame count: {stats['frame_count']}")
        print(f"🚗 Unique vehicles: {stats['tracking_stats']['total_unique']}")
        
        # Register webhook (optional)
        print("\n🔗 Registering webhook...")
        try:
            webhook = client.register_webhook(
                url="https://your-server.com/webhook",
                events=["vehicle_detected", "unique_vehicle_counted"]
            )
            print(f"✅ Webhook registered: {webhook['webhook_id']}")
        except Exception as e:
            print(f"⚠️ Webhook registration failed: {e}")
        
        # List all streams
        print("\n📋 Listing all streams...")
        streams = client.list_streams()
        for stream in streams:
            print(f"  - {stream['stream_id']}: {stream['stream_url'][:50]}...")
        
        # Clean up - delete the stream
        print(f"\n🗑️ Cleaning up - deleting stream {stream_id}...")
        client.delete_stream(stream_id)
        print("✅ Stream deleted")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

# Additional utility functions

def monitor_stream_continuously(client: CCTVAPIClient, stream_id: str, duration: int = 60):
    """
    Monitor a stream continuously for a specified duration
    
    Args:
        client: CCTVAPIClient instance
        stream_id: Stream ID to monitor
        duration: Duration in seconds
    """
    print(f"🔍 Monitoring stream {stream_id} for {duration} seconds...")
    
    start_time = time.time()
    last_detection_count = 0
    
    while time.time() - start_time < duration:
        try:
            detections = client.get_detections(stream_id)
            current_count = len(detections)
            
            if current_count != last_detection_count:
                print(f"🚗 Detection count changed: {current_count}")
                last_detection_count = current_count
            
            time.sleep(5)  # Check every 5 seconds
            
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            break
    
    print("✅ Monitoring completed")

def batch_create_streams(client: CCTVAPIClient, stream_urls: List[str]) -> List[str]:
    """
    Create multiple streams in batch
    
    Args:
        client: CCTVAPIClient instance
        stream_urls: List of stream URLs
        
    Returns:
        List of created stream IDs
    """
    stream_ids = []
    
    for i, url in enumerate(stream_urls):
        try:
            stream = client.create_stream(url, f"Batch Stream {i+1}")
            stream_ids.append(stream['stream_id'])
            print(f"✅ Created stream {i+1}: {stream['stream_id']}")
        except Exception as e:
            print(f"❌ Failed to create stream {i+1}: {e}")
    
    return stream_ids

def export_statistics_to_csv(client: CCTVAPIClient, stream_id: str, filename: str):
    """
    Export stream statistics to CSV file
    
    Args:
        client: CCTVAPIClient instance
        stream_id: Stream ID
        filename: Output CSV filename
    """
    import csv
    from datetime import datetime
    
    try:
        stats = client.get_statistics(stream_id)
        
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(['Timestamp', 'Stream ID', 'Frame Count', 'Total Detections', 'Unique Vehicles'])
            
            # Write data
            writer.writerow([
                datetime.now().isoformat(),
                stream_id,
                stats['frame_count'],
                stats['detection_stats']['total_detections'],
                stats['tracking_stats']['total_unique']
            ])
            
            # Write vehicle breakdown
            writer.writerow([])
            writer.writerow(['Vehicle Type', 'Count'])
            for vehicle_type, count in stats['tracking_stats']['by_type'].items():
                writer.writerow([vehicle_type, count])
        
        print(f"✅ Statistics exported to {filename}")
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
