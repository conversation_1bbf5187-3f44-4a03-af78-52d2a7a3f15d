const { MarineTrafficScraper } = require('./marine-scraper-production');
const fs = require('fs').promises;
const path = require('path');

/**
 * Scheduler untuk menjalankan scraper secara otomatis
 */
class MarineTrafficScheduler {
    constructor(options = {}) {
        this.options = {
            interval: 30 * 60 * 1000, // 30 menit default
            maxRetries: 3,
            retryDelay: 5 * 60 * 1000, // 5 menit
            outputDir: path.join(__dirname, 'data'),
            logFile: path.join(__dirname, 'scraper.log'),
            ...options
        };

        this.isRunning = false;
        this.intervalId = null;
        this.retryCount = 0;
    }

    /**
     * Log message dengan timestamp
     */
    async log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${level}] ${message}`;

        console.log(logMessage);

        try {
            await fs.appendFile(this.options.logFile, logMessage + '\n');
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    /**
     * Ensure output directory exists
     */
    async ensureOutputDir() {
        try {
            await fs.mkdir(this.options.outputDir, { recursive: true });
        } catch (error) {
            await this.log(`Failed to create output directory: ${error.message}`, 'ERROR');
            throw error;
        }
    }

    /**
     * Run single scraping operation
     */
    async runScraper() {
        try {
            await this.log('Starting scraping operation...');

            // Ensure output directory exists
            await this.ensureOutputDir();

            const timestamp = Date.now();
            const outputPath = path.join(this.options.outputDir, `marine-traffic-${timestamp}.json`);

            const scraper = new MarineTrafficScraper({
                headless: true,
                outputPath: outputPath
            });

            const data = await scraper.run();

            const vesselCount = data.data ? data.data.length : 0;
            const totalCount = data.totalCount || 'N/A';
            await this.log(`Scraping completed successfully. Found ${vesselCount} vessels (Total: ${totalCount}). Saved to: ${outputPath}`);

            // Reset retry count on success
            this.retryCount = 0;

            return data;

        } catch (error) {
            await this.log(`Scraping failed: ${error.message}`, 'ERROR');

            this.retryCount++;

            if (this.retryCount <= this.options.maxRetries) {
                await this.log(`Retrying in ${this.options.retryDelay / 1000} seconds... (Attempt ${this.retryCount}/${this.options.maxRetries})`, 'WARN');

                setTimeout(() => {
                    this.runScraper();
                }, this.options.retryDelay);
            } else {
                await this.log(`Max retries (${this.options.maxRetries}) exceeded. Giving up.`, 'ERROR');
                this.retryCount = 0; // Reset for next scheduled run
            }

            throw error;
        }
    }

    /**
     * Start scheduled scraping
     */
    async start() {
        if (this.isRunning) {
            await this.log('Scheduler is already running', 'WARN');
            return;
        }

        try {
            await this.ensureOutputDir();
            await this.log(`Starting Marine Traffic Scheduler with ${this.options.interval / 1000} second intervals`);

            this.isRunning = true;

            // Run immediately
            await this.runScraper();

            // Schedule recurring runs
            this.intervalId = setInterval(async () => {
                if (this.retryCount === 0) { // Only run if not in retry mode
                    await this.runScraper();
                }
            }, this.options.interval);

            await this.log('Scheduler started successfully');

        } catch (error) {
            await this.log(`Failed to start scheduler: ${error.message}`, 'ERROR');
            this.isRunning = false;
            throw error;
        }
    }

    /**
     * Stop scheduled scraping
     */
    async stop() {
        if (!this.isRunning) {
            await this.log('Scheduler is not running', 'WARN');
            return;
        }

        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        this.isRunning = false;
        await this.log('Scheduler stopped');
    }

    /**
     * Get scheduler status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            interval: this.options.interval,
            retryCount: this.retryCount,
            maxRetries: this.options.maxRetries,
            outputDir: this.options.outputDir
        };
    }

    /**
     * Clean old data files (keep only last N files)
     */
    async cleanOldData(keepCount = 10) {
        try {
            const files = await fs.readdir(this.options.outputDir);
            const dataFiles = files
                .filter(file => file.startsWith('marine-traffic-') && file.endsWith('.json'))
                .map(file => ({
                    name: file,
                    path: path.join(this.options.outputDir, file),
                    timestamp: parseInt(file.match(/marine-traffic-(\d+)\.json/)?.[1] || '0')
                }))
                .sort((a, b) => b.timestamp - a.timestamp);

            if (dataFiles.length > keepCount) {
                const filesToDelete = dataFiles.slice(keepCount);

                for (const file of filesToDelete) {
                    await fs.unlink(file.path);
                    await this.log(`Deleted old data file: ${file.name}`);
                }

                await this.log(`Cleaned ${filesToDelete.length} old data files`);
            }

        } catch (error) {
            await this.log(`Failed to clean old data: ${error.message}`, 'ERROR');
        }
    }
}

// CLI interface
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0] || 'start';

    const scheduler = new MarineTrafficScheduler({
        interval: 30 * 60 * 1000, // 30 minutes
        maxRetries: 3,
        retryDelay: 5 * 60 * 1000 // 5 minutes
    });

    switch (command) {
        case 'start':
            console.log('🕐 Starting Marine Traffic Scheduler...');
            scheduler.start().catch(error => {
                console.error('Failed to start scheduler:', error.message);
                process.exit(1);
            });

            // Handle graceful shutdown
            process.on('SIGINT', async () => {
                console.log('\n🛑 Received SIGINT, stopping scheduler...');
                await scheduler.stop();
                process.exit(0);
            });

            process.on('SIGTERM', async () => {
                console.log('\n🛑 Received SIGTERM, stopping scheduler...');
                await scheduler.stop();
                process.exit(0);
            });
            break;

        case 'once':
            console.log('🚀 Running scraper once...');
            scheduler.runScraper()
                .then(() => {
                    console.log('✅ Single run completed');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('❌ Single run failed:', error.message);
                    process.exit(1);
                });
            break;

        case 'clean':
            const keepCount = parseInt(args[1]) || 10;
            console.log(`🧹 Cleaning old data files (keeping last ${keepCount})...`);
            scheduler.cleanOldData(keepCount)
                .then(() => {
                    console.log('✅ Cleanup completed');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('❌ Cleanup failed:', error.message);
                    process.exit(1);
                });
            break;

        default:
            console.log(`
Usage: node scheduler.js [command]

Commands:
  start     Start the scheduler (default)
  once      Run scraper once and exit
  clean [n] Clean old data files (keep last n files, default: 10)

Examples:
  node scheduler.js start
  node scheduler.js once
  node scheduler.js clean 5
            `);
            process.exit(0);
    }
}

module.exports = { MarineTrafficScheduler };
