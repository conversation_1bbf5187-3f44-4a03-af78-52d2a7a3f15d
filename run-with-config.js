/**
 * Script untuk menjalankan Marine Traffic Scraper dengan konfigurasi
 */

const { MarineTrafficScraper } = require('./marine-scraper-production');
const { MarineTrafficScheduler } = require('./scheduler');
const fs = require('fs');
const path = require('path');

/**
 * Load configuration file
 */
function loadConfig() {
    const configPath = path.join(__dirname, 'config.js');
    const exampleConfigPath = path.join(__dirname, 'config.example.js');

    if (fs.existsSync(configPath)) {
        console.log('📋 Loading configuration from config.js...');
        return require('./config');
    } else if (fs.existsSync(exampleConfigPath)) {
        console.log('⚠️  config.js not found, using config.example.js');
        console.log('💡 Copy config.example.js to config.js and customize it');
        return require('./config.example');
    } else {
        console.log('📋 Using default configuration');
        return getDefaultConfig();
    }
}

/**
 * Get default configuration
 */
function getDefaultConfig() {
    return {
        scraper: {
            headless: true,
            timeout: 30000,
            sessionPath: path.join(__dirname, 'sessions', 'session.json'),
            outputPath: path.join(__dirname, 'marine-traffic-data.json')
        },
        scheduler: {
            interval: 30 * 60 * 1000,
            maxRetries: 3,
            retryDelay: 5 * 60 * 1000,
            outputDir: path.join(__dirname, 'data'),
            logFile: path.join(__dirname, 'scraper.log'),
            keepDataFiles: 10
        }
    };
}

/**
 * Run scraper once with configuration
 */
async function runOnce() {
    try {
        const config = loadConfig();
        console.log('🚀 Running Marine Traffic Scraper once...\n');

        const scraper = new MarineTrafficScraper(config.scraper);
        const data = await scraper.run();

        console.log('\n✅ Scraping completed successfully!');
        console.log(`📊 Found ${data.data ? data.data.length : 0} vessels`);
        console.log(`📈 Total count from API: ${data.totalCount || 'N/A'}`);
        console.log(`💾 Data saved to: ${config.scraper.outputPath}`);

        return data;

    } catch (error) {
        console.error('\n❌ Scraping failed:', error.message);
        throw error;
    }
}

/**
 * Run scheduler with configuration
 */
async function runScheduler() {
    try {
        const config = loadConfig();
        console.log('🕐 Starting Marine Traffic Scheduler...\n');

        const scheduler = new MarineTrafficScheduler(config.scheduler);

        // Handle graceful shutdown
        process.on('SIGINT', async () => {
            console.log('\n🛑 Received SIGINT, stopping scheduler...');
            await scheduler.stop();
            process.exit(0);
        });

        process.on('SIGTERM', async () => {
            console.log('\n🛑 Received SIGTERM, stopping scheduler...');
            await scheduler.stop();
            process.exit(0);
        });

        await scheduler.start();

    } catch (error) {
        console.error('\n❌ Scheduler failed:', error.message);
        throw error;
    }
}

/**
 * Clean old data files
 */
async function cleanData(keepCount = null) {
    try {
        const config = loadConfig();
        const finalKeepCount = keepCount || config.scheduler.keepDataFiles || 10;

        console.log(`🧹 Cleaning old data files (keeping last ${finalKeepCount})...\n`);

        const scheduler = new MarineTrafficScheduler(config.scheduler);
        await scheduler.cleanOldData(finalKeepCount);

        console.log('\n✅ Cleanup completed!');

    } catch (error) {
        console.error('\n❌ Cleanup failed:', error.message);
        throw error;
    }
}

/**
 * Show configuration
 */
function showConfig() {
    try {
        const config = loadConfig();
        console.log('📋 Current Configuration:\n');
        console.log(JSON.stringify(config, null, 2));

    } catch (error) {
        console.error('❌ Failed to load configuration:', error.message);
    }
}

/**
 * Show help
 */
function showHelp() {
    console.log(`
🌊 Marine Traffic Scraper with Configuration

Usage: node run-with-config.js [command] [options]

Commands:
  once              Run scraper once and exit
  scheduler         Start the scheduler (continuous mode)
  clean [n]         Clean old data files (keep last n files)
  config            Show current configuration
  help              Show this help message

Examples:
  node run-with-config.js once
  node run-with-config.js scheduler
  node run-with-config.js clean 5
  node run-with-config.js config

Configuration:
  1. Copy config.example.js to config.js
  2. Customize the settings in config.js
  3. Run the scraper with your custom configuration

Notes:
  - If config.js doesn't exist, config.example.js will be used
  - If neither exists, default configuration will be used
  - Check scraper.log for detailed logging information
    `);
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'help';

    switch (command) {
        case 'once':
            await runOnce();
            break;

        case 'scheduler':
            await runScheduler();
            break;

        case 'clean':
            const keepCount = parseInt(args[1]) || null;
            await cleanData(keepCount);
            break;

        case 'config':
            showConfig();
            break;

        case 'help':
        default:
            showHelp();
            break;
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('\n💥 Execution failed:', error.message);
        process.exit(1);
    });
}

module.exports = {
    loadConfig,
    runOnce,
    runScheduler,
    cleanData,
    showConfig
};
