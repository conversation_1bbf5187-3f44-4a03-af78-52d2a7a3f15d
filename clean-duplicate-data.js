const fs = require('fs').promises;
const path = require('path');

/**
 * Script untuk membersihkan data duplikat dan menghasilkan format yang sama dengan download.json
 */

async function cleanDuplicateData(inputFile, outputFile) {
    try {
        console.log('🧹 Starting data cleaning process...');
        console.log(`📂 Input file: ${inputFile}`);
        console.log(`📂 Output file: ${outputFile}`);
        
        // Read input file
        console.log('📖 Reading input file...');
        const rawData = await fs.readFile(inputFile, 'utf8');
        const data = JSON.parse(rawData);
        
        if (!data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid data format');
        }
        
        console.log(`📊 Original data count: ${data.data.length}`);
        
        // Remove duplicates based on SHIP_ID
        console.log('🔍 Removing duplicates based on SHIP_ID...');
        const uniqueVessels = [];
        const seenShipIds = new Set();
        
        for (const vessel of data.data) {
            if (vessel.SHIP_ID && !seenShipIds.has(vessel.SHIP_ID)) {
                seenShipIds.add(vessel.SHIP_ID);
                uniqueVessels.push(vessel);
            }
        }
        
        console.log(`✅ Unique vessels: ${uniqueVessels.length}`);
        console.log(`🗑️  Duplicates removed: ${data.data.length - uniqueVessels.length}`);
        
        // Create clean data in the same format as download.json
        const cleanData = {
            data: uniqueVessels
        };
        
        // Add totalCount if it exists
        if (data.totalCount) {
            cleanData.totalCount = data.totalCount;
        }
        
        // Save clean data
        console.log('💾 Saving clean data...');
        await fs.writeFile(outputFile, JSON.stringify(cleanData), 'utf8');
        
        console.log('\n🎉 Data cleaning completed!');
        console.log(`📊 Final vessel count: ${uniqueVessels.length}`);
        console.log(`💾 Clean data saved to: ${outputFile}`);
        
        // Show some statistics
        const countryStats = {};
        const typeStats = {};
        
        uniqueVessels.forEach(vessel => {
            // Country statistics
            const country = vessel.COUNTRY || 'Unknown';
            countryStats[country] = (countryStats[country] || 0) + 1;
            
            // Type statistics
            const type = vessel.TYPE_SUMMARY || 'Unknown';
            typeStats[type] = (typeStats[type] || 0) + 1;
        });
        
        console.log('\n📈 Statistics:');
        console.log('🏳️  Top 10 Countries:');
        Object.entries(countryStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([country, count]) => {
                console.log(`   ${country}: ${count} vessels`);
            });
        
        console.log('\n🚢 Ship Types:');
        Object.entries(typeStats)
            .sort(([,a], [,b]) => b - a)
            .forEach(([type, count]) => {
                console.log(`   ${type}: ${count} vessels`);
            });
        
        return cleanData;
        
    } catch (error) {
        console.error('❌ Error cleaning data:', error.message);
        throw error;
    }
}

/**
 * Find the latest full data file
 */
async function findLatestFullDataFile() {
    try {
        const files = await fs.readdir(__dirname);
        const fullDataFiles = files
            .filter(file => file.startsWith('marine-traffic-full-') && file.endsWith('.json'))
            .map(file => ({
                name: file,
                path: path.join(__dirname, file),
                timestamp: parseInt(file.match(/marine-traffic-full-(\d+)\.json/)?.[1] || '0')
            }))
            .sort((a, b) => b.timestamp - a.timestamp);
        
        if (fullDataFiles.length === 0) {
            throw new Error('No full data files found. Run "npm run scrape-full" first.');
        }
        
        return fullDataFiles[0].path;
    } catch (error) {
        console.error('❌ Error finding latest file:', error.message);
        throw error;
    }
}

/**
 * Main execution
 */
async function main() {
    try {
        const args = process.argv.slice(2);
        let inputFile = args[0];
        let outputFile = args[1];
        
        // If no input file specified, find the latest one
        if (!inputFile) {
            console.log('🔍 No input file specified, finding latest full data file...');
            inputFile = await findLatestFullDataFile();
            console.log(`✅ Found latest file: ${path.basename(inputFile)}`);
        }
        
        // If no output file specified, create one
        if (!outputFile) {
            const timestamp = Date.now();
            outputFile = path.join(__dirname, `marine-traffic-clean-${timestamp}.json`);
        }
        
        // Clean the data
        await cleanDuplicateData(inputFile, outputFile);
        
    } catch (error) {
        console.error('💥 Execution failed:', error.message);
        process.exit(1);
    }
}

// Export for module use
module.exports = { cleanDuplicateData, findLatestFullDataFile };

// Run if called directly
if (require.main === module) {
    main();
}
