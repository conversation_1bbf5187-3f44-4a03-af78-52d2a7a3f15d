const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

/**
 * Marine Traffic Data Scraper
 * Mengambil data kapal dari MarineTraffic.com menggunakan session yang tersimpan
 */
class MarineTrafficScraper {
    constructor(options = {}) {
        this.options = {
            headless: true,
            sessionPath: path.join(__dirname, 'sessions', 'session.json'),
            outputPath: path.join(__dirname, 'marine-traffic-data.json'),
            timeout: 30000,
            ...options
        };

        this.browser = null;
        this.context = null;
        this.page = null;
    }

    /**
     * Initialize browser and load session
     */
    async init() {
        try {
            console.log('🚀 Initializing Marine Traffic Scraper...');

            // Launch browser
            this.browser = await chromium.launch({
                headless: this.options.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            // Create context
            this.context = await this.browser.newContext({
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
                viewport: { width: 1920, height: 1080 },
                locale: 'en-US',
                timezoneId: 'Asia/Jakarta'
            });

            // Load session cookies
            await this.loadSession();

            // Create page
            this.page = await this.context.newPage();

            // Set headers
            await this.page.setExtraHTTPHeaders({
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,id;q=0.8',
                'referer': 'https://www.marinetraffic.com/en/data/?asset_type=vessels&columns=flag%2Cshipname%2Cphoto%2Crecognized_next_port%2Creported_eta%2Creported_destination%2Ccurrent_port%2Cimo%2Cship_type%2Cshow_on_live_map%2Ctime_of_latest_position%2Clat_of_latest_position%2Clon_of_latest_position%2Cnotes&current_port_country_in=ID%7CIndonesia',
                'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest'
            });

            console.log('✅ Browser initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing browser:', error.message);
            throw error;
        }
    }

    /**
     * Load session cookies from file
     */
    async loadSession() {
        try {
            const sessionData = await fs.readFile(this.options.sessionPath, 'utf8');
            const cookies = JSON.parse(sessionData);

            await this.context.addCookies(cookies);
            console.log(`🍪 Loaded ${cookies.length} cookies from session`);
        } catch (error) {
            console.error('❌ Error loading session:', error.message);
            throw error;
        }
    }

    /**
     * Fetch marine traffic data
     */
    async fetchData() {
        try {
            console.log('🌐 Fetching marine traffic data...');

            // URL untuk mengambil semua data - coba berbagai parameter
            const apiUrl = 'https://www.marinetraffic.com/en/reports/?asset_type=vessels&columns=flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes&current_port_country_in=ID&filters_with_name_filtering=yard_number_in&limit=50000&per_page=50000&page_size=50000';

            const response = await this.page.goto(apiUrl, {
                waitUntil: 'networkidle',
                timeout: this.options.timeout
            });

            if (!response.ok()) {
                throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
            }

            // Get response content
            const responseText = await this.page.content();

            // Extract JSON from HTML response
            let data;
            if (responseText.includes('<pre>') && responseText.includes('</pre>')) {
                const jsonMatch = responseText.match(/<pre>(.*?)<\/pre>/s);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[1]);
                    console.log('✅ Successfully extracted JSON from HTML response');
                } else {
                    throw new Error('Could not extract JSON from HTML');
                }
            } else {
                data = JSON.parse(responseText);
            }

            return data;

        } catch (error) {
            console.error('❌ Error fetching data:', error.message);
            throw error;
        }
    }

    /**
     * Save data to file
     */
    async saveData(data) {
        try {
            await fs.writeFile(this.options.outputPath, JSON.stringify(data, null, 2), 'utf8');
            console.log(`💾 Data saved to: ${this.options.outputPath}`);
        } catch (error) {
            console.error('❌ Error saving data:', error.message);
            throw error;
        }
    }

    /**
     * Process and format data - keep raw format like download.json
     */
    processData(rawData) {
        if (!rawData || !rawData.data || !Array.isArray(rawData.data)) {
            throw new Error('Invalid data format received');
        }

        // Return raw data format exactly like download.json
        return rawData;
    }

    /**
     * Close browser
     */
    async close() {
        try {
            if (this.page) await this.page.close();
            if (this.context) await this.context.close();
            if (this.browser) await this.browser.close();
            console.log('🧹 Browser closed successfully');
        } catch (error) {
            console.error('❌ Error closing browser:', error.message);
        }
    }

    /**
     * Main execution method
     */
    async run() {
        try {
            await this.init();
            const rawData = await this.fetchData();
            const processedData = this.processData(rawData);
            await this.saveData(processedData);

            console.log('\n📊 Summary:');
            console.log(`   Total vessels found: ${processedData.data ? processedData.data.length : 0}`);
            console.log(`   Total count from API: ${processedData.totalCount || 'N/A'}`);
            console.log(`   Data timestamp: ${new Date().toISOString()}`);
            console.log(`   Output file: ${this.options.outputPath}`);

            return processedData;

        } catch (error) {
            console.error('❌ Scraper execution failed:', error.message);
            throw error;
        } finally {
            await this.close();
        }
    }
}

/**
 * Quick run function
 */
async function quickRun(options = {}) {
    const scraper = new MarineTrafficScraper(options);
    return await scraper.run();
}

// Export for module use
module.exports = { MarineTrafficScraper, quickRun };

// Run if called directly
if (require.main === module) {
    const options = {
        headless: true, // Set to false for debugging
        outputPath: path.join(__dirname, `marine-traffic-${Date.now()}.json`)
    };

    quickRun(options)
        .then(() => {
            console.log('\n🎉 Scraping completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Scraping failed:', error.message);
            process.exit(1);
        });
}
