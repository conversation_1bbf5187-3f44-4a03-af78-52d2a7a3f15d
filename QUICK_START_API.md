# 🚀 Quick Start - Marine Traffic API

## 🌊 Apa itu Marine Traffic API?

Marine Traffic API adalah server REST API yang menyediakan akses lengkap ke data kapal di perairan Indonesia. Data diambil dari MarineTraffic.com dengan format yang sama persis seperti download.json.

## ⚡ Quick Start (5 Menit)

### 1. Install & Start Server
```bash
# Clone/download project
cd "Marine Traffic"

# Install dependencies
npm install
npm run install-browsers

# Start server
npm start
```

### 2. Akses API
Server akan berjalan di: **http://localhost:3000**

- 🌐 **Web Interface**: http://localhost:3000
- 📊 **All Vessels**: http://localhost:3000/api/vessels
- 📈 **Statistics**: http://localhost:3000/api/vessels/stats
- 🔍 **Search**: http://localhost:3000/api/vessels/search?country=Indonesia

## 🎯 Contoh Penggunaan

### JavaScript (Fetch API)
```javascript
// Get all vessels
const response = await fetch('http://localhost:3000/api/vessels');
const data = await response.json();
console.log(`Total vessels: ${data.total}`);

// Search Indonesian cargo ships
const search = await fetch('http://localhost:3000/api/vessels/search?country=Indonesia&type=Cargo');
const results = await search.json();
console.log(`Found ${results.total} Indonesian cargo ships`);

// Get statistics
const stats = await fetch('http://localhost:3000/api/vessels/stats');
const statistics = await stats.json();
console.log(`Countries: ${Object.keys(statistics.statistics.countries).length}`);
```

### Python (requests)
```python
import requests

# Get all vessels
response = requests.get('http://localhost:3000/api/vessels')
data = response.json()
print(f"Total vessels: {data['total']}")

# Search by name
search = requests.get('http://localhost:3000/api/vessels/search', 
                     params={'name': 'SAGA'})
results = search.json()
print(f"Found {results['total']} vessels named SAGA")
```

### cURL
```bash
# Get all vessels
curl http://localhost:3000/api/vessels

# Search Indonesian tankers
curl "http://localhost:3000/api/vessels/search?country=Indonesia&type=Tanker"

# Get vessel by ID
curl http://localhost:3000/api/vessels/1283618

# Get statistics
curl http://localhost:3000/api/vessels/stats
```

## 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/vessels` | Semua data kapal |
| GET | `/api/vessels/stats` | Statistik lengkap |
| GET | `/api/vessels/search` | Pencarian dengan filter |
| GET | `/api/vessels/:id` | Data kapal berdasarkan ID |
| POST | `/api/scrape` | Mulai scraping baru |
| GET | `/api/scrape/status` | Status scraping |
| GET | `/api/files` | Daftar file data |
| GET | `/api/health` | Health check |

## 🔍 Search Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `name` | Nama kapal | `?name=SAGA` |
| `country` | Negara | `?country=Indonesia` |
| `type` | Jenis kapal | `?type=Cargo` |
| `port` | Pelabuhan | `?port=JAKARTA` |
| `imo` | IMO number | `?imo=7911545` |
| `mmsi` | MMSI number | `?mmsi=210121000` |

### Kombinasi Filter
```bash
# Indonesian cargo ships in Jakarta
curl "http://localhost:3000/api/vessels/search?country=Indonesia&type=Cargo&port=JAKARTA"

# All tankers
curl "http://localhost:3000/api/vessels/search?type=Tanker"
```

## 📊 Response Format

### Vessels Response
```json
{
  "success": true,
  "timestamp": "2025-08-19T05:41:28.586Z",
  "total": 500,
  "data": [
    {
      "SHIP_ID": "1283618",
      "IMO": "7911545",
      "SHIPNAME": "SAGA",
      "COUNTRY": "Cyprus",
      "TYPE_SUMMARY": "Passenger",
      "CURRENT_PORT": "BATU AMPAR",
      "LAT": "1.0983417",
      "LON": "103.89544",
      "SPEED": "0.0",
      "COURSE": "184"
    }
  ]
}
```

### Statistics Response
```json
{
  "success": true,
  "statistics": {
    "total": 500,
    "indonesianVessels": 241,
    "foreignVessels": 259,
    "topCountries": [
      ["Indonesia", 241],
      ["Panama", 59],
      ["Liberia", 48]
    ],
    "topShipTypes": [
      ["Cargo", 314],
      ["Tanker", 125],
      ["Special Craft", 31]
    ]
  }
}
```

## 🚀 Advanced Usage

### Start Background Scraping
```javascript
// Trigger new scraping
const response = await fetch('http://localhost:3000/api/scrape', {
    method: 'POST'
});
const result = await response.json();
console.log(result.message); // "Scraping started"

// Check status
const status = await fetch('http://localhost:3000/api/scrape/status');
const statusData = await status.json();
console.log(statusData.isInProgress); // true/false
```

### Real-time Monitoring
```javascript
// Auto-refresh data every 30 seconds
setInterval(async () => {
    const response = await fetch('http://localhost:3000/api/vessels/stats');
    const data = await response.json();
    console.log(`Updated: ${data.statistics.total} vessels`);
}, 30000);
```

## 🌐 Web Interface

Akses http://localhost:3000 untuk:
- 📖 **Interactive Documentation**
- 🧪 **API Testing Interface**
- 📊 **Real-time Statistics Dashboard**
- 🔍 **Search Testing Tools**

## 🛠️ Configuration

### Environment Variables
```bash
PORT=3000  # Server port (default: 3000)
```

### Custom Port
```bash
PORT=8080 npm start  # Run on port 8080
```

## 📈 Data Coverage

- 🚢 **500+ unique vessels** di perairan Indonesia
- 🏳️ **39 countries** represented
- ⚓ **6 ship types** (Cargo, Tanker, Passenger, etc.)
- 🇮🇩 **241 Indonesian vessels** (48.2%)
- 🌍 **259 foreign vessels** (51.8%)

## 🔧 Troubleshooting

### Server tidak start
```bash
# Check if port 3000 is available
netstat -an | findstr :3000

# Use different port
PORT=8080 npm start
```

### No data available
```bash
# Run scraping first
curl -X POST http://localhost:3000/api/scrape

# Check status
curl http://localhost:3000/api/scrape/status
```

### CORS issues
API sudah mengaktifkan CORS untuk semua origins. Jika masih ada masalah, pastikan menggunakan HTTPS jika diperlukan.

## 🎉 Next Steps

1. **Integrate dengan aplikasi Anda**
2. **Setup monitoring dashboard**
3. **Automate data collection**
4. **Build analytics tools**

## 📞 Support

- 📖 **Full Documentation**: `API_DOCUMENTATION.md`
- 📋 **Complete Guide**: `README.md`
- 📊 **Project Summary**: `SUMMARY.md`

---

**Happy Coding! 🚢⚓**
