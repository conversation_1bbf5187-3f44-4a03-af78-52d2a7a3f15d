# Marine Traffic API Documentation

## 🌊 Overview

Marine Traffic API menyediakan akses programatis ke data kapal di perairan Indonesia yang diambil dari MarineTraffic.com. API ini menggunakan format data yang sama persis dengan download.json dari MarineTraffic.

**Base URL:** `http://localhost:3000`

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start server:**
   ```bash
   npm start
   ```

3. **Access API:**
   - Documentation: http://localhost:3000
   - All vessels: http://localhost:3000/api/vessels
   - Statistics: http://localhost:3000/api/vessels/stats

## 📋 API Endpoints

### 1. Get All Vessels
```
GET /api/vessels
```

**Description:** Mengambil semua data kapal yang tersedia

**Response:**
```json
{
  "success": true,
  "timestamp": "2025-08-19T05:41:28.586Z",
  "total": 500,
  "data": [
    {
      "SHIP_ID": "1283618",
      "IMO": "7911545",
      "MMSI": "210121000",
      "CALLSIGN": "5BRC5",
      "SHIPNAME": "SAGA",
      "COUNTRY": "Cyprus",
      "TYPE_SUMMARY": "Passenger",
      "CURRENT_PORT": "BATU AMPAR",
      "LAT": "1.0983417",
      "LON": "103.89544",
      "SPEED": "0.0",
      "COURSE": "184"
    }
  ]
}
```

### 2. Get Vessel Statistics
```
GET /api/vessels/stats
```

**Description:** Mengambil statistik lengkap tentang kapal

**Response:**
```json
{
  "success": true,
  "timestamp": "2025-08-19T05:41:28.586Z",
  "statistics": {
    "total": 500,
    "indonesianVessels": 241,
    "foreignVessels": 259,
    "countries": {
      "Indonesia": 241,
      "Panama": 59,
      "Liberia": 48
    },
    "topCountries": [
      ["Indonesia", 241],
      ["Panama", 59],
      ["Liberia", 48]
    ],
    "topShipTypes": [
      ["Cargo", 314],
      ["Tanker", 125],
      ["Special Craft", 31]
    ],
    "topPorts": [
      ["BATU AMPAR", 45],
      ["JAKARTA", 38],
      ["SURABAYA", 25]
    ]
  }
}
```

### 3. Search Vessels
```
GET /api/vessels/search
```

**Description:** Mencari kapal berdasarkan kriteria tertentu

**Query Parameters:**
- `name` - Nama kapal (partial match, case insensitive)
- `country` - Negara (partial match, case insensitive)
- `type` - Jenis kapal (partial match, case insensitive)
- `port` - Pelabuhan saat ini (partial match, case insensitive)
- `imo` - IMO number (partial match)
- `mmsi` - MMSI number (partial match)

**Examples:**
```
GET /api/vessels/search?name=SAGA
GET /api/vessels/search?country=Indonesia
GET /api/vessels/search?type=Cargo
GET /api/vessels/search?port=JAKARTA
GET /api/vessels/search?country=Indonesia&type=Tanker
```

**Response:**
```json
{
  "success": true,
  "query": {
    "country": "Indonesia",
    "type": "Tanker"
  },
  "total": 45,
  "data": [...]
}
```

### 4. Get Vessel by ID
```
GET /api/vessels/:id
```

**Description:** Mengambil data kapal berdasarkan SHIP_ID

**Example:**
```
GET /api/vessels/1283618
```

**Response:**
```json
{
  "success": true,
  "data": {
    "SHIP_ID": "1283618",
    "IMO": "7911545",
    "SHIPNAME": "SAGA",
    ...
  }
}
```

### 5. Start Scraping
```
POST /api/scrape
```

**Description:** Memulai proses scraping data baru dari MarineTraffic

**Response:**
```json
{
  "success": true,
  "message": "Scraping started",
  "status": "in_progress"
}
```

### 6. Get Scraping Status
```
GET /api/scrape/status
```

**Description:** Mengecek status proses scraping

**Response:**
```json
{
  "isInProgress": false,
  "lastResult": {
    "success": true,
    "timestamp": "2025-08-19T05:41:28.586Z",
    "vesselsCount": 500,
    "outputPath": "/path/to/marine-traffic-complete-1755581980191.json"
  }
}
```

### 7. List Data Files
```
GET /api/files
```

**Description:** Melihat daftar file data yang tersedia

**Response:**
```json
{
  "success": true,
  "total": 3,
  "files": [
    {
      "name": "marine-traffic-complete-1755581980191.json",
      "size": 2048576,
      "created": "2025-08-19T05:41:28.586Z",
      "modified": "2025-08-19T05:41:28.586Z",
      "timestamp": "2025-08-19T05:41:28.586Z"
    }
  ]
}
```

### 8. Health Check
```
GET /api/health
```

**Description:** Mengecek status kesehatan server

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-08-19T05:41:28.586Z",
  "uptime": 3600,
  "memory": {
    "rss": 50331648,
    "heapTotal": 20971520,
    "heapUsed": 15728640
  }
}
```

## 🔍 Data Format

API menggunakan format data yang sama persis dengan download.json dari MarineTraffic:

### Vessel Object Fields:
- `SHIP_ID` - ID unik kapal
- `IMO` - International Maritime Organization number
- `MMSI` - Maritime Mobile Service Identity
- `CALLSIGN` - Call sign kapal
- `SHIPNAME` - Nama kapal
- `COUNTRY` - Negara bendera
- `CODE2` - Kode negara 2 huruf
- `TYPE_SUMMARY` - Jenis kapal (Cargo, Tanker, Passenger, dll)
- `CURRENT_PORT` - Pelabuhan saat ini
- `CURRENT_PORT_COUNTRY` - Negara pelabuhan saat ini
- `NEXT_PORT_NAME` - Pelabuhan tujuan berikutnya
- `DESTINATION` - Tujuan yang dilaporkan
- `LAT` - Latitude posisi terakhir
- `LON` - Longitude posisi terakhir
- `SPEED` - Kecepatan (knots)
- `COURSE` - Arah (degrees)
- `LAST_POS` - Timestamp posisi terakhir (Unix timestamp)
- `ETA` - Estimated Time of Arrival (Unix timestamp)
- `COUNT_PHOTOS` - Jumlah foto kapal

## 📊 Statistics

API menyediakan statistik lengkap:
- Total kapal
- Breakdown per negara
- Breakdown per jenis kapal
- Breakdown per pelabuhan
- Kapal Indonesia vs asing
- Top 10 negara
- Top 20 pelabuhan

## 🚨 Error Handling

Semua endpoint menggunakan HTTP status codes standar:
- `200` - Success
- `404` - Not Found
- `409` - Conflict (scraping already in progress)
- `500` - Internal Server Error

Error response format:
```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

## 🔄 CORS

API mendukung CORS untuk akses dari browser. Semua origins diizinkan untuk development.

## 📝 Usage Examples

### JavaScript (Fetch API)
```javascript
// Get all vessels
const response = await fetch('http://localhost:3000/api/vessels');
const data = await response.json();
console.log(`Total vessels: ${data.total}`);

// Search Indonesian cargo ships
const searchResponse = await fetch('http://localhost:3000/api/vessels/search?country=Indonesia&type=Cargo');
const searchData = await searchResponse.json();
console.log(`Found ${searchData.total} Indonesian cargo ships`);
```

### Python (requests)
```python
import requests

# Get statistics
response = requests.get('http://localhost:3000/api/vessels/stats')
data = response.json()
print(f"Total vessels: {data['statistics']['total']}")

# Search by name
search_response = requests.get('http://localhost:3000/api/vessels/search', 
                              params={'name': 'SAGA'})
search_data = search_response.json()
print(f"Found {search_data['total']} vessels matching 'SAGA'")
```

### cURL
```bash
# Get all vessels
curl http://localhost:3000/api/vessels

# Search vessels
curl "http://localhost:3000/api/vessels/search?country=Indonesia&type=Tanker"

# Start scraping
curl -X POST http://localhost:3000/api/scrape

# Check scraping status
curl http://localhost:3000/api/scrape/status
```

## 🔧 Configuration

Server dapat dikonfigurasi melalui environment variables:
- `PORT` - Port server (default: 3000)

## 📈 Performance

- API menggunakan file-based storage untuk performa optimal
- Data di-cache dalam memory untuk response yang cepat
- Mendukung concurrent requests
- Scraping berjalan di background tanpa blocking API

## 🛡️ Security

- Input validation pada semua endpoints
- Rate limiting (dapat ditambahkan)
- CORS configuration
- Error handling yang aman
