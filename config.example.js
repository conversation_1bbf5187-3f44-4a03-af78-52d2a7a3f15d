/**
 * Contoh konfigurasi untuk Marine Traffic Scraper
 * Salin file ini ke config.js dan sesuaikan dengan kebutuhan <PERSON>a
 */

const path = require('path');

module.exports = {
    // Konfigurasi Scraper
    scraper: {
        // Mode headless browser (true untuk production, false untuk debugging)
        headless: true,
        
        // Timeout untuk request (dalam milidetik)
        timeout: 30000,
        
        // Path ke file session cookies
        sessionPath: path.join(__dirname, 'sessions', 'session.json'),
        
        // Path output default
        outputPath: path.join(__dirname, 'marine-traffic-data.json')
    },

    // Konfigurasi Scheduler
    scheduler: {
        // Interval scraping (dalam milidetik)
        // 30 menit = 30 * 60 * 1000
        // 1 jam = 60 * 60 * 1000
        // 6 jam = 6 * 60 * 60 * 1000
        interval: 30 * 60 * 1000,
        
        // Maksimal retry jika gagal
        maxRetries: 3,
        
        // Delay sebelum retry (dalam milidetik)
        retryDelay: 5 * 60 * 1000,
        
        // Direktori untuk menyimpan data
        outputDir: path.join(__dirname, 'data'),
        
        // File log
        logFile: path.join(__dirname, 'scraper.log'),
        
        // Jumlah file data yang disimpan (file lama akan dihapus)
        keepDataFiles: 10
    },

    // Konfigurasi API MarineTraffic
    api: {
        // Base URL
        baseUrl: 'https://www.marinetraffic.com',
        
        // Endpoint untuk data kapal
        endpoint: '/en/reports/',
        
        // Parameter query default
        defaultParams: {
            asset_type: 'vessels',
            columns: 'flag,shipname,photo,recognized_next_port,reported_eta,reported_destination,current_port,imo,ship_type,show_on_live_map,time_of_latest_position,lat_of_latest_position,lon_of_latest_position,notes',
            current_port_country_in: 'ID',
            filters_with_name_filtering: 'yard_number_in'
        },
        
        // Headers yang diperlukan
        headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,id;q=0.8',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-requested-with': 'XMLHttpRequest'
        }
    },

    // Konfigurasi Browser
    browser: {
        // User agent
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        
        // Viewport
        viewport: {
            width: 1920,
            height: 1080
        },
        
        // Locale
        locale: 'en-US',
        
        // Timezone
        timezoneId: 'Asia/Jakarta',
        
        // Browser args
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    },

    // Konfigurasi Data Processing
    dataProcessing: {
        // Format timestamp (ISO string atau unix timestamp)
        timestampFormat: 'iso', // 'iso' atau 'unix'
        
        // Include raw data dalam output
        includeRawData: false,
        
        // Field mapping untuk data yang diproses
        fieldMapping: {
            ship_id: 'SHIP_ID',
            imo: 'IMO',
            mmsi: 'MMSI',
            callsign: 'CALLSIGN',
            shipname: 'SHIPNAME',
            country: 'COUNTRY',
            flag: 'CODE2',
            ship_type: 'TYPE_SUMMARY',
            current_port: 'CURRENT_PORT',
            current_port_country: 'CURRENT_PORT_COUNTRY',
            next_port: 'NEXT_PORT_NAME',
            next_port_country: 'NEXT_PORT_COUNTRY',
            destination: 'DESTINATION',
            eta: 'ETA',
            last_position_time: 'LAST_POS',
            latitude: 'LAT',
            longitude: 'LON',
            speed: 'SPEED',
            course: 'COURSE',
            photos_count: 'COUNT_PHOTOS'
        }
    },

    // Konfigurasi Logging
    logging: {
        // Level log (error, warn, info, debug)
        level: 'info',
        
        // Format log
        format: '[{timestamp}] [{level}] {message}',
        
        // Maksimal ukuran file log (dalam bytes)
        maxFileSize: 10 * 1024 * 1024, // 10MB
        
        // Jumlah file log backup
        maxFiles: 5
    }
};

// Contoh penggunaan:
// const config = require('./config');
// const scraper = new MarineTrafficScraper(config.scraper);
// const scheduler = new MarineTrafficScheduler(config.scheduler);
