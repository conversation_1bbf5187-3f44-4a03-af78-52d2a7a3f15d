# Serverless Admin Key Configuration Guide

## Overview

Panduan ini menjelaskan cara mengkonfigurasi static admin key untuk deployment serverless CCTV monitoring system di berbagai platform cloud.

## Default Serverless Admin Key

```
Production: cctv_admin_prod_2024
```

Key ini digunakan secara default untuk semua deployment serverless production.

## Platform-Specific Configuration

### 1. Google Cloud Run

#### Deployment dengan Admin Key
```bash
gcloud run deploy cctv-monitoring \
  --image gcr.io/YOUR_PROJECT_ID/cctv-monitoring \
  --platform managed \
  --region asia-southeast2 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 300 \
  --set-env-vars SERVERLESS=true,FLASK_ENV=production,FLASK_DEBUG=0,TORCH_HOME=/app/.cache/torch,XDG_CACHE_HOME=/app/.cache,CCTV_ADMIN_KEY=cctv_admin_prod_2024
```

#### Update Admin Key untuk Service yang Sudah Ada
```bash
gcloud run services update cctv-monitoring \
  --region asia-southeast2 \
  --set-env-vars CCTV_ADMIN_KEY=your_custom_admin_key
```

#### Menggunakan Secret Manager (Recommended untuk Production)
```bash
# Buat secret
echo "your_secure_admin_key_2024" | gcloud secrets create cctv-admin-key --data-file=-

# Deploy dengan secret
gcloud run deploy cctv-monitoring \
  --image gcr.io/YOUR_PROJECT_ID/cctv-monitoring \
  --set-env-vars CCTV_ADMIN_KEY_SECRET=cctv-admin-key \
  --region asia-southeast2
```

### 2. AWS Lambda (Serverless Framework)

#### serverless.yml Configuration
```yaml
provider:
  name: aws
  runtime: python3.11
  environment:
    SERVERLESS: true
    FLASK_ENV: production
    FLASK_DEBUG: 0
    TORCH_HOME: /tmp/.cache/torch
    XDG_CACHE_HOME: /tmp/.cache
    CCTV_ADMIN_KEY: cctv_admin_prod_2024

# Untuk production, gunakan AWS Systems Manager Parameter Store
custom:
  adminKey: ${ssm:/cctv/admin-key}

provider:
  environment:
    CCTV_ADMIN_KEY: ${self:custom.adminKey}
```

#### Deploy dengan Custom Admin Key
```bash
# Set parameter di AWS Systems Manager
aws ssm put-parameter \
  --name "/cctv/admin-key" \
  --value "your_secure_admin_key_2024" \
  --type "SecureString"

# Deploy
serverless deploy --stage prod
```

### 3. AWS Fargate

#### Task Definition dengan Admin Key
```json
{
  "family": "cctv-monitoring",
  "containerDefinitions": [
    {
      "name": "cctv-app",
      "image": "your-account.dkr.ecr.region.amazonaws.com/cctv-monitoring:latest",
      "environment": [
        {"name": "SERVERLESS", "value": "true"},
        {"name": "FLASK_ENV", "value": "production"},
        {"name": "CCTV_ADMIN_KEY", "value": "cctv_admin_prod_2024"}
      ]
    }
  ]
}
```

#### Menggunakan AWS Secrets Manager
```json
{
  "secrets": [
    {
      "name": "CCTV_ADMIN_KEY",
      "valueFrom": "arn:aws:secretsmanager:region:account:secret:cctv-admin-key"
    }
  ]
}
```

### 4. Azure Container Instances

#### Deployment dengan Admin Key
```bash
az container create \
  --resource-group cctv-monitoring-rg \
  --name cctv-monitoring \
  --image myregistry.azurecr.io/cctv-monitoring:latest \
  --cpu 1 \
  --memory 2 \
  --ports 8080 \
  --environment-variables \
    SERVERLESS=true \
    FLASK_ENV=production \
    CCTV_ADMIN_KEY=cctv_admin_prod_2024
```

#### Menggunakan Azure Key Vault
```bash
# Buat secret di Key Vault
az keyvault secret set \
  --vault-name cctv-keyvault \
  --name cctv-admin-key \
  --value "your_secure_admin_key_2024"

# Deploy dengan Key Vault reference
az container create \
  --resource-group cctv-monitoring-rg \
  --name cctv-monitoring \
  --image myregistry.azurecr.io/cctv-monitoring:latest \
  --secure-environment-variables \
    CCTV_ADMIN_KEY=@Microsoft.KeyVault(SecretUri=https://cctv-keyvault.vault.azure.net/secrets/cctv-admin-key/)
```

## Custom Admin Key Configuration

### 1. Environment Variable
```bash
# Set custom admin key
export CCTV_ADMIN_KEY="your_custom_serverless_key_2024"

# Build dan deploy
docker build --target serverless -t cctv-monitoring-serverless .
```

### 2. Docker Build dengan Custom Key
```dockerfile
# Dockerfile.serverless
FROM cctv-monitoring-serverless as custom
ENV CCTV_ADMIN_KEY=your_custom_key_2024
```

```bash
docker build -f Dockerfile.serverless -t cctv-monitoring-custom .
```

### 3. Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cctv-monitoring
spec:
  template:
    spec:
      containers:
      - name: cctv-app
        image: cctv-monitoring:latest
        env:
        - name: SERVERLESS
          value: "true"
        - name: FLASK_ENV
          value: "production"
        - name: CCTV_ADMIN_KEY
          valueFrom:
            secretKeyRef:
              name: cctv-admin-secret
              key: admin-key
```

## Testing Serverless Admin Key

### 1. Test dengan Curl
```bash
# Ganti YOUR_SERVICE_URL dengan URL serverless Anda
SERVICE_URL="https://your-service-url.com"

# Test health endpoint
curl -H "X-API-Key: cctv_admin_prod_2024" \
     $SERVICE_URL/api/v1/system/health

# Test admin endpoints
curl -H "X-API-Key: cctv_admin_prod_2024" \
     $SERVICE_URL/api/v1/auth/keys
```

### 2. Test Script untuk Serverless
```python
#!/usr/bin/env python3
import requests
import os

# Serverless service URL
SERVICE_URL = os.environ.get('CCTV_SERVICE_URL', 'https://your-service-url.com')
ADMIN_KEY = os.environ.get('CCTV_ADMIN_KEY', 'cctv_admin_prod_2024')

def test_serverless_admin():
    headers = {'X-API-Key': ADMIN_KEY}
    
    # Test health
    response = requests.get(f"{SERVICE_URL}/api/v1/system/health", headers=headers)
    print(f"Health Check: {response.status_code}")
    
    # Test admin access
    response = requests.get(f"{SERVICE_URL}/api/v1/auth/keys", headers=headers)
    print(f"Admin Access: {response.status_code}")

if __name__ == "__main__":
    test_serverless_admin()
```

## Security Best Practices

### 1. Production Keys
```bash
# Gunakan key yang kuat untuk production
CCTV_ADMIN_KEY="cctv_prod_$(openssl rand -hex 16)_2024"
```

### 2. Key Rotation
```bash
# Script untuk rotate admin key
#!/bin/bash
NEW_KEY="cctv_admin_$(date +%Y%m%d)_$(openssl rand -hex 8)"

# Update di Google Cloud Run
gcloud run services update cctv-monitoring \
  --region asia-southeast2 \
  --set-env-vars CCTV_ADMIN_KEY=$NEW_KEY

echo "Admin key rotated to: $NEW_KEY"
```

### 3. Monitoring
```bash
# Monitor API usage dengan admin key
gcloud logs read --service cctv-monitoring \
  --filter='httpRequest.headers."X-API-Key"="cctv_admin_prod_2024"' \
  --limit 100
```

## Troubleshooting

### 1. Admin Key Tidak Bekerja
```bash
# Cek environment variables di container
gcloud run services describe cctv-monitoring \
  --region asia-southeast2 \
  --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)"
```

### 2. Authentication Errors
```bash
# Test dengan verbose output
curl -v -H "X-API-Key: cctv_admin_prod_2024" \
     https://your-service-url.com/api/v1/system/health
```

### 3. Environment Variable Issues
```bash
# Cek logs untuk environment variable loading
gcloud logs read --service cctv-monitoring \
  --filter='textPayload:"admin API key"' \
  --limit 10
```

## Migration dari Random Key

### 1. Update Existing Service
```bash
# Google Cloud Run
gcloud run services update cctv-monitoring \
  --region asia-southeast2 \
  --set-env-vars CCTV_ADMIN_KEY=cctv_admin_prod_2024

# AWS Lambda
serverless deploy --stage prod
```

### 2. Update Client Applications
```python
# Update semua client code
OLD_KEY = "cctv_admin_xyz123random"
NEW_KEY = "cctv_admin_prod_2024"

# Ganti di semua API calls
```

## Support

Untuk bantuan lebih lanjut:
1. Cek dokumentasi platform cloud yang digunakan
2. Test dengan script yang disediakan
3. Monitor logs untuk authentication errors
4. Pastikan environment variables ter-set dengan benar
